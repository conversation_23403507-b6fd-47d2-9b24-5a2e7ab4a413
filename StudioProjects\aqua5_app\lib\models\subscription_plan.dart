class SubscriptionPlan {
  final String id;
  final String planName;
  final double price;
  final double dailyUsage; // in liters
  final int planValidityMonths;
  final DateTime createdAt;
  final DateTime updatedAt;
  final bool isActive;
  final List<String> features;

  SubscriptionPlan({
    required this.id,
    required this.planName,
    required this.price,
    required this.dailyUsage,
    required this.planValidityMonths,
    required this.createdAt,
    required this.updatedAt,
    this.isActive = true,
    this.features = const [],
  });

  // Convert from Firestore document
  factory SubscriptionPlan.fromMap(Map<String, dynamic> map, String documentId) {
    return SubscriptionPlan(
      id: documentId,
      planName: map['planName'] ?? '',
      price: (map['price'] ?? 0).toDouble(),
      dailyUsage: (map['dailyUsage'] ?? 0).toDouble(),
      planValidityMonths: map['planValidityMonths'] ?? 1,
      createdAt: map['createdAt']?.toDate() ?? DateTime.now(),
      updatedAt: map['updatedAt']?.toDate() ?? DateTime.now(),
      isActive: map['isActive'] ?? true,
      features: List<String>.from(map['features'] ?? []),
    );
  }

  // Convert to Firestore document
  Map<String, dynamic> toMap() {
    return {
      'planName': planName,
      'price': price,
      'dailyUsage': dailyUsage,
      'planValidityMonths': planValidityMonths,
      'createdAt': createdAt,
      'updatedAt': updatedAt,
      'isActive': isActive,
      'features': features,
    };
  }

  // Create a copy with updated fields
  SubscriptionPlan copyWith({
    String? id,
    String? planName,
    double? price,
    double? dailyUsage,
    int? planValidityMonths,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool? isActive,
    List<String>? features,
  }) {
    return SubscriptionPlan(
      id: id ?? this.id,
      planName: planName ?? this.planName,
      price: price ?? this.price,
      dailyUsage: dailyUsage ?? this.dailyUsage,
      planValidityMonths: planValidityMonths ?? this.planValidityMonths,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isActive: isActive ?? this.isActive,
      features: features ?? this.features,
    );
  }

  @override
  String toString() {
    return 'SubscriptionPlan(id: $id, planName: $planName, price: $price, dailyUsage: $dailyUsage, planValidityMonths: $planValidityMonths)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is SubscriptionPlan && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  // Helper method to get plan validity as readable string
  String get planValidityString {
    if (planValidityMonths == 1) {
      return '1 Month';
    } else if (planValidityMonths == 12) {
      return '1 Year';
    } else {
      return '$planValidityMonths Months';
    }
  }

  // Helper method to get daily usage as readable string
  String get dailyUsageString {
    if (dailyUsage == double.infinity) {
      return 'Unlimited';
    } else {
      return '${dailyUsage.toInt()}L';
    }
  }

  // Helper method to get price as readable string
  String get priceString {
    return '\$${price.toStringAsFixed(2)}';
  }
}
