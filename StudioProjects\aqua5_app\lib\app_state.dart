import 'package:flutter/material.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'mqtt_service.dart';
import 'package:go_router/go_router.dart';
import 'models/subscription_plan.dart';
import 'services/firestore_service.dart';

class AppState extends ChangeNotifier {
  final FirebaseAuth _auth = FirebaseAuth.instance;
  User? currentUser;
  bool isAdmin = false;

  // User Data
  String userId = 'user_123';
  String userEmail = '<EMAIL>';
  double tdsLevel = 46;
  double dailyUsage = 1.8;
  double monthlyUsage = 45.2;
  double quota = 2.0;
  bool isConnected = true;
  bool isFilterActive = true;
  String subscriptionPlan = 'Premium';
  String expiryDate = '05/15/2025';
  List<Map<String, dynamic>> paymentHistory = [
    {
      'plan': 'SLAGS',
      'duration': '1 Month',
      'amount': 29.99,
      'date': '04/15/2025',
      'status': 'Success'
    },
    {
      'plan': 'SLAGS',
      'duration': '1 Month',
      'amount': 29.99,
      'date': '03/15/2025',
      'status': 'Success'
    },
  ];
  List<String> notifications = [
    'Water tank full - 05/01/2025 09:30 AM',
    'Subscription expiring soon - 05/01/2025 08:00 AM',
    'Filter replacement needed - 04/30/2025 03:15 PM',
  ];

  // Admin Data
  List<Map<String, dynamic>> devices = [
    {
      'id': 'device_001',
      'name': 'Device 1',
      'user': 'User A (<EMAIL>)',
      'status': 'Connected',
      'tds': 42.0,
      'usage': 1.5
    },
    {
      'id': 'device_002',
      'name': 'Device 2',
      'user': 'User B (<EMAIL>)',
      'status': 'Connected',
      'tds': 48.0,
      'usage': 1.7
    },
    {
      'id': 'device_003',
      'name': 'Device 3',
      'user': 'User C (<EMAIL>)',
      'status': 'Disconnected',
      'tds': 0.0,
      'usage': 0.0
    },
  ];
  List<Map<String, dynamic>> deviceLogs = [
    {'device': 'Device 1', 'message': 'Started - 05/01/2025 09:00 AM'},
    {
      'device': 'Device 2',
      'message': 'TDS Updated: 48 ppm - 05/01/2025 08:45 AM'
    },
    {'device': 'Device 3', 'message': 'Disconnected - 05/01/2025 07:30 AM'},
  ];
  List<Map<String, dynamic>> allPayments = [
    {
      'user': 'User A',
      'transactionId': 'txn_001',
      'amount': 29.99,
      'date': '04/15/2025',
      'plan': 'SLAGS',
      'status': 'Success'
    },
    {
      'user': 'User B',
      'transactionId': 'txn_002',
      'amount': 29.99,
      'date': '04/10/2025',
      'plan': 'SLAGS',
      'status': 'Success'
    },
    {
      'user': 'User C',
      'transactionId': 'txn_003',
      'amount': 29.99,
      'date': '04/05/2025',
      'plan': 'SLAGS',
      'status': 'Failed'
    },
  ];

  // Subscription Plans Data
  List<SubscriptionPlan> subscriptionPlans = [];
  bool isLoadingPlans = false;
  final FirestoreService _firestoreService = FirestoreService();

  final MQTTService mqttService =
      MQTTService('client_${DateTime.now().millisecondsSinceEpoch}');

  AppState() {
    _auth.authStateChanges().listen((User? user) {
      currentUser = user;
      if (user != null) {
        userId = user.uid;
        userEmail = user.email ?? 'Unknown';
        // Mock admin check (in real app, check Firestore for admin role)
        isAdmin = user.email == '<EMAIL>';
      } else {
        userId = 'user_123';
        userEmail = '<EMAIL>';
        isAdmin = false;
      }
      notifyListeners();
    });

    mqttService.onMessage = (topic, payload) {
      if (topic.contains('status')) {
        final deviceId = topic.split('/')[1];
        final deviceIndex = devices.indexWhere((d) => d['id'] == deviceId);
        if (deviceIndex != -1) {
          devices[deviceIndex]['status'] =
              payload == 'ON' ? 'Connected' : 'Disconnected';
          notifyListeners();
        }
      }
    };
    mqttService.connect();

    // Load subscription plans on initialization
    loadSubscriptionPlans();
  }

  Future<void> signUp(String email, String password) async {
    try {
      await _auth.createUserWithEmailAndPassword(
          email: email, password: password);
    } catch (e) {
      throw Exception('Sign-up failed: $e');
    }
  }

  Future<void> signIn(String email, String password) async {
    try {
      await _auth.signInWithEmailAndPassword(email: email, password: password);
    } catch (e) {
      throw Exception('Sign-in failed: $e');
    }
  }

  Future<void> signInWithPhone(
      String phoneNumber, Function(String) onCodeSent) async {
    try {
      await _auth.verifyPhoneNumber(
        phoneNumber: phoneNumber,
        verificationCompleted: (PhoneAuthCredential credential) async {
          await _auth.signInWithCredential(credential);
        },
        verificationFailed: (FirebaseAuthException e) {
          throw Exception('Phone verification failed: $e');
        },
        codeSent: (String verificationId, int? resendToken) {
          onCodeSent(verificationId);
        },
        codeAutoRetrievalTimeout: (String verificationId) {},
      );
    } catch (e) {
      throw Exception('Phone sign-in failed: $e');
    }
  }

  Future<void> verifyOTP(String verificationId, String smsCode) async {
    try {
      final credential = PhoneAuthProvider.credential(
        verificationId: verificationId,
        smsCode: smsCode,
      );
      await _auth.signInWithCredential(credential);
    } catch (e) {
      throw Exception('OTP verification failed: $e');
    }
  }

  Future<void> signOut() async {
    await _auth.signOut();
  }

  void updateTdsLevel(double newLevel) {
    tdsLevel = newLevel;
    notifyListeners();
  }

  void toggleDevice(String deviceId, bool isOn) {
    final deviceIndex = devices.indexWhere((d) => d['id'] == deviceId);
    if (deviceIndex != -1) {
      devices[deviceIndex]['status'] = isOn ? 'Connected' : 'Disconnected';
      mqttService.publish('device/$deviceId/control', isOn ? 'ON' : 'OFF');
      deviceLogs.add({
        'device': devices[deviceIndex]['name'],
        'message':
            '${isOn ? 'Started' : 'Stopped'} - ${DateTime.now().toString().substring(0, 16)}',
      });
      notifyListeners();
    }
  }

  void bindDevice(String deviceId) {
    final existingDevice =
        devices.firstWhere((d) => d['id'] == deviceId, orElse: () => {});
    if (existingDevice.isEmpty) {
      devices.add({
        'id': deviceId,
        'name': 'Device ${devices.length + 1}',
        'user': '$userEmail',
        'status': 'Connected',
        'tds': 45.0,
        'usage': 0.0,
      });
      mqttService.publish('device/$deviceId/bind', userId);
      notifyListeners();
    }
  }

  void renewSubscription(String plan, double amount) {
    paymentHistory.add({
      'plan': plan,
      'duration': '1 Month',
      'amount': amount,
      'date': DateTime.now().toString().substring(0, 10),
      'status': 'Success',
    });
    expiryDate = DateTime.now()
        .add(const Duration(days: 30))
        .toString()
        .substring(0, 10);
    subscriptionPlan = plan;
    notifyListeners();
  }

  // Navigation method to centralize navigation logic
  void navigateTo(BuildContext context, String route) {
    // You can add analytics tracking, permission checks, etc. here
    Navigator.pushNamed(context, route);
  }

  // Subscription Plans Management Methods
  Future<void> loadSubscriptionPlans() async {
    try {
      isLoadingPlans = true;
      notifyListeners();

      subscriptionPlans = await _firestoreService.getPlans();
      print(subscriptionPlans);

      // Create default plans if none exist
      if (subscriptionPlans.isEmpty) {
        await _firestoreService.createDefaultPlans();
        subscriptionPlans = await _firestoreService.getPlans();
      }
    } catch (e) {
      debugPrint('Error loading subscription plans: $e');
    } finally {
      isLoadingPlans = false;
      notifyListeners();
    }
  }

  Future<void> addSubscriptionPlan(SubscriptionPlan plan) async {
    try {
      await _firestoreService.addPlan(plan);
      await loadSubscriptionPlans(); // Refresh the list
    } catch (e) {
      throw Exception('Failed to add plan: $e');
    }
  }

  Future<void> updateSubscriptionPlan(
      String planId, SubscriptionPlan plan) async {
    try {
      await _firestoreService.updatePlan(planId, plan);
      await loadSubscriptionPlans(); // Refresh the list
    } catch (e) {
      throw Exception('Failed to update plan: $e');
    }
  }

  Future<void> deleteSubscriptionPlan(String planId) async {
    try {
      await _firestoreService.deletePlan(planId);
      await loadSubscriptionPlans(); // Refresh the list
    } catch (e) {
      throw Exception('Failed to delete plan: $e');
    }
  }

  Future<bool> checkPlanNameExists(String planName,
      {String? excludePlanId}) async {
    try {
      return await _firestoreService.planNameExists(planName,
          excludePlanId: excludePlanId);
    } catch (e) {
      debugPrint('Error checking plan name: $e');
      return false;
    }
  }

  SubscriptionPlan? getPlanById(String planId) {
    try {
      return subscriptionPlans.firstWhere((plan) => plan.id == planId);
    } catch (e) {
      return null;
    }
  }

  SubscriptionPlan? getPlanByName(String planName) {
    try {
      return subscriptionPlans.firstWhere((plan) => plan.planName == planName);
    } catch (e) {
      return null;
    }
  }
}
