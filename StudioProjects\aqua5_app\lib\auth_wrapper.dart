import 'package:flutter/material.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:provider/provider.dart';
import 'app_state.dart';
import 'screens/login_screen.dart';
import 'screens/base_page.dart';

/// A wrapper widget that handles authentication state changes
/// and redirects users based on their authentication status
class AuthWrapper extends StatelessWidget {
  const AuthWrapper({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<User?>(
      stream: FirebaseAuth.instance.authStateChanges(),
      builder: (context, snapshot) {
        // If the snapshot has user data, then they're already signed in
        if (snapshot.hasData && snapshot.data != null) {
          // Update the app state with the current user
          final appState = Provider.of<AppState>(context, listen: false);
          appState.currentUser = snapshot.data;
          
          // Return the main app UI
          return const BasePage(
            currentIndex: 0,
            showFloatingActionButton: true,
            child: SizedBox(), // This will be replaced by _getPage in BasePage
          );
        }
        
        // If the snapshot doesn't have data, or if the data is null,
        // the user isn't signed in, so show the login screen
        return const LoginScreen();
      },
    );
  }
}
