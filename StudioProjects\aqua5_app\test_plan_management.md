# Testing Plan Management System

## Prerequisites
1. Run `flutter pub get` to install cloud_firestore dependency
2. Ensure Firebase project is properly configured
3. Make sure admin user is logged in (<EMAIL>)

## Test Cases

### 1. Add New Plan
- Navigate to Admin → Plans tab
- Click "Add Plan" button
- Fill in all fields:
  - Plan Name: "Test Plan"
  - Price: 15.99
  - Daily Usage: 25 (or check unlimited)
  - Plan Validity: 2 months
  - Features: "Feature 1, Feature 2, Feature 3"
- Click "Add" button
- Verify plan appears in the list

### 2. Edit Existing Plan
- Click the menu button (⋮) on any plan card
- Select "Edit"
- Modify some fields
- Click "Update" button
- Verify changes are reflected

### 3. Delete Plan
- Click the menu button (⋮) on any plan card
- Select "Delete"
- Confirm deletion in dialog
- Verify plan is removed from list

### 4. Form Validation
- Try adding plan with empty name → Should show error
- Try adding plan with invalid price → Should show error
- Try adding plan with duplicate name → Should show error

### 5. Loading States
- Check loading indicator when plans are being fetched
- Check empty state when no plans exist

### 6. Firestore Integration
- Check Firebase Console → Firestore Database
- Verify "subscription_plans" collection is created
- Verify plan documents have correct structure

## Expected Behavior
- Plans should persist across app restarts
- Real-time updates when plans are modified
- Proper error handling and user feedback
- Consistent UI with rest of the app

## Troubleshooting
If you encounter issues:
1. Check Firebase configuration
2. Verify internet connection
3. Check console for error messages
4. Ensure Firestore rules allow read/write access
