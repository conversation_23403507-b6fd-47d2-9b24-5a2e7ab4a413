^C:\USERS\<USER>\STUDIOPROJECTS\AQUA5_APP\BUILD\WINDOWS\X64\CMAKEFILES\10E551854EB81E7039D247E64F85DE7F\GENERATE.STAMP.RULE
setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SC:/Users/<USER>/StudioProjects/aqua5_app/windows -BC:/Users/<USER>/StudioProjects/aqua5_app/build/windows/x64 --check-stamp-list CMakeFiles/generate.stamp.list --vs-solution-file C:/Users/<USER>/StudioProjects/aqua5_app/build/windows/x64/aqua5_app.sln
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
