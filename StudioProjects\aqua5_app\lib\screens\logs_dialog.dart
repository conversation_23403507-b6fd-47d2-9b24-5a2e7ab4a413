import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../app_state.dart';
import '../colors.dart';

class LogsDialog extends StatelessWidget {
  const LogsDialog({super.key});

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      backgroundColor: Colors.white,
      title: const Text(
        'Device Logs',
        style: TextStyle(
          color: Color(0xFF212121),
          fontWeight: FontWeight.bold,
        ),
      ),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      content: SizedBox(
        width: double.maxFinite,
        child: Consumer<AppState>(
          builder: (context, appState, child) {
            return ListView.builder(
              shrinkWrap: true,
              itemCount: appState.deviceLogs.length,
              itemBuilder: (context, index) {
                final log = appState.deviceLogs[index];
                return ListTile(
                  leading: CircleAvatar(
                    backgroundColor: AppColors.accent.withAlpha(50),
                    child: Icon(
                      Icons.device_unknown,
                      color: AppColors.accent,
                      size: 20,
                    ),
                  ),
                  title: Text(
                    '${log['device']}',
                    style: const TextStyle(
                      color: Color(0xFF212121),
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  subtitle: Text(
                    '${log['message']}',
                    style: const TextStyle(color: Color(0xFF757575)),
                  ),
                );
              },
            );
          },
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: Text(
            'Close',
            style: TextStyle(
              color: AppColors.accent,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
      ],
    );
  }
}
