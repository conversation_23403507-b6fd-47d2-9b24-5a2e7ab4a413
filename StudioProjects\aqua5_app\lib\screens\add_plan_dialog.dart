import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../models/subscription_plan.dart';
import '../services/firestore_service.dart';
import '../colors.dart';

class AddPlanDialog extends StatefulWidget {
  final SubscriptionPlan? existingPlan;
  final VoidCallback? onPlanAdded;

  const AddPlanDialog({
    super.key,
    this.existingPlan,
    this.onPlanAdded,
  });

  @override
  State<AddPlanDialog> createState() => _AddPlanDialogState();
}

class _AddPlanDialogState extends State<AddPlanDialog> {
  final _formKey = GlobalKey<FormState>();
  final _planNameController = TextEditingController();
  final _priceController = TextEditingController();
  final _dailyUsageController = TextEditingController();
  final _featuresController = TextEditingController();
  
  int _selectedValidityMonths = 1;
  bool _isLoading = false;
  bool _isUnlimitedUsage = false;

  final List<int> _validityOptions = [1, 2, 3, 6, 12];

  @override
  void initState() {
    super.initState();
    if (widget.existingPlan != null) {
      _populateFields(widget.existingPlan!);
    }
  }

  void _populateFields(SubscriptionPlan plan) {
    _planNameController.text = plan.planName;
    _priceController.text = plan.price.toString();
    _selectedValidityMonths = plan.planValidityMonths;
    _featuresController.text = plan.features.join(', ');
    
    if (plan.dailyUsage == double.infinity) {
      _isUnlimitedUsage = true;
      _dailyUsageController.text = '';
    } else {
      _isUnlimitedUsage = false;
      _dailyUsageController.text = plan.dailyUsage.toString();
    }
  }

  @override
  void dispose() {
    _planNameController.dispose();
    _priceController.dispose();
    _dailyUsageController.dispose();
    _featuresController.dispose();
    super.dispose();
  }

  Future<void> _savePlan() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isLoading = true);

    try {
      // Check if plan name already exists (excluding current plan if editing)
      final planNameExists = await FirestoreService().planNameExists(
        _planNameController.text.trim(),
        excludePlanId: widget.existingPlan?.id,
      );

      if (planNameExists) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('A plan with this name already exists'),
              backgroundColor: Colors.red,
            ),
          );
        }
        return;
      }

      final features = _featuresController.text
          .split(',')
          .map((f) => f.trim())
          .where((f) => f.isNotEmpty)
          .toList();

      final plan = SubscriptionPlan(
        id: widget.existingPlan?.id ?? '',
        planName: _planNameController.text.trim(),
        price: double.parse(_priceController.text),
        dailyUsage: _isUnlimitedUsage 
            ? double.infinity 
            : double.parse(_dailyUsageController.text),
        planValidityMonths: _selectedValidityMonths,
        createdAt: widget.existingPlan?.createdAt ?? DateTime.now(),
        updatedAt: DateTime.now(),
        features: features,
      );

      if (widget.existingPlan != null) {
        await FirestoreService().updatePlan(widget.existingPlan!.id, plan);
      } else {
        await FirestoreService().addPlan(plan);
      }

      if (mounted) {
        Navigator.of(context).pop();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(widget.existingPlan != null 
                ? 'Plan updated successfully' 
                : 'Plan added successfully'),
            backgroundColor: Colors.green,
          ),
        );
        widget.onPlanAdded?.call();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      backgroundColor: Colors.white,
      title: Text(
        widget.existingPlan != null ? 'Edit Plan' : 'Add New Plan',
        style: const TextStyle(
          color: Color(0xFF212121),
          fontWeight: FontWeight.bold,
        ),
      ),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      content: SizedBox(
        width: double.maxFinite,
        child: Form(
          key: _formKey,
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Plan Name Field
                TextFormField(
                  controller: _planNameController,
                  decoration: InputDecoration(
                    labelText: 'Plan Name',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    prefixIcon: const Icon(Icons.label),
                  ),
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return 'Please enter a plan name';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),

                // Price Field
                TextFormField(
                  controller: _priceController,
                  decoration: InputDecoration(
                    labelText: 'Price (\$)',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    prefixIcon: const Icon(Icons.attach_money),
                  ),
                  keyboardType: const TextInputType.numberWithOptions(decimal: true),
                  inputFormatters: [
                    FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*')),
                  ],
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return 'Please enter a price';
                    }
                    final price = double.tryParse(value);
                    if (price == null || price <= 0) {
                      return 'Please enter a valid price';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),

                // Daily Usage Field with Unlimited Option
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Checkbox(
                          value: _isUnlimitedUsage,
                          onChanged: (value) {
                            setState(() {
                              _isUnlimitedUsage = value ?? false;
                              if (_isUnlimitedUsage) {
                                _dailyUsageController.clear();
                              }
                            });
                          },
                        ),
                        const Text('Unlimited Usage'),
                      ],
                    ),
                    if (!_isUnlimitedUsage)
                      TextFormField(
                        controller: _dailyUsageController,
                        decoration: InputDecoration(
                          labelText: 'Daily Usage (Liters)',
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                          prefixIcon: const Icon(Icons.water_drop),
                        ),
                        keyboardType: const TextInputType.numberWithOptions(decimal: true),
                        inputFormatters: [
                          FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*')),
                        ],
                        validator: (value) {
                          if (_isUnlimitedUsage) return null;
                          if (value == null || value.trim().isEmpty) {
                            return 'Please enter daily usage';
                          }
                          final usage = double.tryParse(value);
                          if (usage == null || usage <= 0) {
                            return 'Please enter a valid usage amount';
                          }
                          return null;
                        },
                      ),
                  ],
                ),
                const SizedBox(height: 16),

                // Plan Validity Dropdown
                DropdownButtonFormField<int>(
                  value: _selectedValidityMonths,
                  decoration: InputDecoration(
                    labelText: 'Plan Validity',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    prefixIcon: const Icon(Icons.schedule),
                  ),
                  items: _validityOptions.map((months) {
                    return DropdownMenuItem(
                      value: months,
                      child: Text(months == 1 ? '1 Month' : '$months Months'),
                    );
                  }).toList(),
                  onChanged: (value) {
                    setState(() {
                      _selectedValidityMonths = value!;
                    });
                  },
                ),
                const SizedBox(height: 16),

                // Features Field
                TextFormField(
                  controller: _featuresController,
                  decoration: InputDecoration(
                    labelText: 'Features (comma separated)',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    prefixIcon: const Icon(Icons.star),
                    hintText: 'e.g., TDS monitoring, Priority support',
                  ),
                  maxLines: 3,
                ),
              ],
            ),
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: _isLoading ? null : () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: _isLoading ? null : _savePlan,
          style: ElevatedButton.styleFrom(
            backgroundColor: AppColors.accent,
            foregroundColor: Colors.white,
          ),
          child: _isLoading
              ? const SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  ),
                )
              : Text(widget.existingPlan != null ? 'Update' : 'Add'),
        ),
      ],
    );
  }
}
