import 'package:aqua5_app/app_state.dart';
import 'package:aqua5_app/constance.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../colors.dart';
import 'package:provider/provider.dart';

class AppDrawer extends StatelessWidget {
  const AppDrawer({super.key});

  @override
  Widget build(BuildContext context) {
    final appState = Provider.of<AppState>(context, listen: false);

    return Drawer(
      backgroundColor: AppColors.cardBackground,
      child: ListView(
        padding: EdgeInsets.zero,
        children: [
          DrawerHeader(
            decoration: const BoxDecoration(
              color: AppColors.accent,
            ),
            child: Text(
              AppConstants.appName,
              style: const TextStyle(
                color: AppColors.textPrimary,
                fontSize: 24,
              ),
            ),
          ),
          ListTile(
            leading: const Icon(Icons.home, color: AppColors.textPrimary),
            title: const Text('Home',
                style: TextStyle(color: AppColors.textPrimary)),
            onTap: () {
              appState.navigateTo(context, '/home');
            },
          ),
          ListTile(
            leading:
                const Icon(Icons.subscriptions, color: AppColors.textPrimary),
            title: const Text('Subscription',
                style: TextStyle(color: AppColors.textPrimary)),
            onTap: () {
              appState.navigateTo(context, '/subscription');
            },
          ),
          ListTile(
            leading:
                const Icon(Icons.notifications, color: AppColors.textPrimary),
            title: const Text('Notifications',
                style: TextStyle(color: AppColors.textPrimary)),
            onTap: () {
              appState.navigateTo(context, '/notifications');
            },
          ),
          ListTile(
            leading: const Icon(Icons.admin_panel_settings,
                color: AppColors.textPrimary),
            title: const Text('Admin',
                style: TextStyle(color: AppColors.textPrimary)),
            onTap: () {
              appState.navigateTo(context, '/admin');
            },
          ),
          ListTile(
            leading: const Icon(Icons.settings, color: AppColors.textPrimary),
            title: const Text('Settings',
                style: TextStyle(color: AppColors.textPrimary)),
            onTap: () {
              appState.navigateTo(context, '/settings');
            },
          ),
          ListTile(
            leading: const Icon(Icons.person, color: AppColors.textPrimary),
            title: const Text('Profile',
                style: TextStyle(color: AppColors.textPrimary)),
            onTap: () {
              appState.navigateTo(context, '/profile');
            },
          ),
          ListTile(
            leading: const Icon(Icons.device_hub, color: AppColors.textPrimary),
            title: const Text('Device Binding',
                style: TextStyle(color: AppColors.textPrimary)),
            onTap: () {
              appState.navigateTo(context, '/device-binding');
            },
          ),
        ],
      ),
    );
  }
}
