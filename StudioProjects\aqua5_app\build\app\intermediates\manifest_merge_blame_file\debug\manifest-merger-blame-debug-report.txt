1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.aqua5_app"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="23"
9        android:targetSdkVersion="35" />
10    <!--
11         The INTERNET permission is required for development. Specifically,
12         the Flutter tool needs it to communicate with the running application
13         to allow setting breakpoints, to provide hot reload, etc.
14    -->
15    <uses-permission android:name="android.permission.INTERNET" />
15-->C:\Users\<USER>\StudioProjects\aqua5_app\android\app\src\debug\AndroidManifest.xml:6:5-66
15-->C:\Users\<USER>\StudioProjects\aqua5_app\android\app\src\debug\AndroidManifest.xml:6:22-64
16    <!--
17     Required to query activities that can process text, see:
18         https://developer.android.com/training/package-visibility and
19         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.
20
21         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin.
22    -->
23    <queries>
23-->C:\Users\<USER>\StudioProjects\aqua5_app\android\app\src\main\AndroidManifest.xml:39:5-44:15
24        <intent>
24-->C:\Users\<USER>\StudioProjects\aqua5_app\android\app\src\main\AndroidManifest.xml:40:9-43:18
25            <action android:name="android.intent.action.PROCESS_TEXT" />
25-->C:\Users\<USER>\StudioProjects\aqua5_app\android\app\src\main\AndroidManifest.xml:41:13-72
25-->C:\Users\<USER>\StudioProjects\aqua5_app\android\app\src\main\AndroidManifest.xml:41:21-70
26
27            <data android:mimeType="text/plain" />
27-->C:\Users\<USER>\StudioProjects\aqua5_app\android\app\src\main\AndroidManifest.xml:42:13-50
27-->C:\Users\<USER>\StudioProjects\aqua5_app\android\app\src\main\AndroidManifest.xml:42:19-48
28        </intent>
29    </queries>
30
31    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
31-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\ebacd37c83e541758107c7e857abb2d5\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:26:5-79
31-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\ebacd37c83e541758107c7e857abb2d5\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:26:22-76
32    <uses-permission android:name="com.google.android.providers.gsf.permission.READ_GSERVICES" />
32-->[com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\5b8b459b9163080c2248098dea79e3eb\transformed\jetified-recaptcha-18.6.1\AndroidManifest.xml:9:5-98
32-->[com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\5b8b459b9163080c2248098dea79e3eb\transformed\jetified-recaptcha-18.6.1\AndroidManifest.xml:9:22-95
33
34    <permission
34-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\b2884dbf4ee3398b2907745768baeed2\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
35        android:name="com.example.aqua5_app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
35-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\b2884dbf4ee3398b2907745768baeed2\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
36        android:protectionLevel="signature" />
36-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\b2884dbf4ee3398b2907745768baeed2\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
37
38    <uses-permission android:name="com.example.aqua5_app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
38-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\b2884dbf4ee3398b2907745768baeed2\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
38-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\b2884dbf4ee3398b2907745768baeed2\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
39
40    <application
41        android:name="android.app.Application"
42        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
42-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\b2884dbf4ee3398b2907745768baeed2\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
43        android:debuggable="true"
44        android:extractNativeLibs="false"
45        android:icon="@mipmap/ic_launcher"
46        android:label="aqua5_app" >
47        <activity
48            android:name="com.example.aqua5_app.MainActivity"
49            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
50            android:exported="true"
51            android:hardwareAccelerated="true"
52            android:launchMode="singleTop"
53            android:taskAffinity=""
54            android:theme="@style/LaunchTheme"
55            android:windowSoftInputMode="adjustResize" >
56
57            <!--
58                 Specifies an Android theme to apply to this Activity as soon as
59                 the Android process has started. This theme is visible to the user
60                 while the Flutter UI initializes. After that, this theme continues
61                 to determine the Window background behind the Flutter UI.
62            -->
63            <meta-data
64                android:name="io.flutter.embedding.android.NormalTheme"
65                android:resource="@style/NormalTheme" />
66
67            <intent-filter>
68                <action android:name="android.intent.action.MAIN" />
69
70                <category android:name="android.intent.category.LAUNCHER" />
71            </intent-filter>
72        </activity>
73        <!--
74             Don't delete the meta-data below.
75             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
76        -->
77        <meta-data
78            android:name="flutterEmbedding"
79            android:value="2" />
80
81        <service
81-->[:cloud_firestore] C:\Users\<USER>\StudioProjects\aqua5_app\build\cloud_firestore\intermediates\merged_manifest\debug\AndroidManifest.xml:8:9-12:19
82            android:name="com.google.firebase.components.ComponentDiscoveryService"
82-->[:cloud_firestore] C:\Users\<USER>\StudioProjects\aqua5_app\build\cloud_firestore\intermediates\merged_manifest\debug\AndroidManifest.xml:8:18-89
83            android:directBootAware="true"
83-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\ae9174fb2605d2700770c56a341d2036\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:32:13-43
84            android:exported="false" >
84-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\ebacd37c83e541758107c7e857abb2d5\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:68:13-37
85            <meta-data
85-->[:cloud_firestore] C:\Users\<USER>\StudioProjects\aqua5_app\build\cloud_firestore\intermediates\merged_manifest\debug\AndroidManifest.xml:9:13-11:85
86                android:name="com.google.firebase.components:io.flutter.plugins.firebase.firestore.FlutterFirebaseFirestoreRegistrar"
86-->[:cloud_firestore] C:\Users\<USER>\StudioProjects\aqua5_app\build\cloud_firestore\intermediates\merged_manifest\debug\AndroidManifest.xml:10:17-134
87                android:value="com.google.firebase.components.ComponentRegistrar" />
87-->[:cloud_firestore] C:\Users\<USER>\StudioProjects\aqua5_app\build\cloud_firestore\intermediates\merged_manifest\debug\AndroidManifest.xml:11:17-82
88            <meta-data
88-->[:firebase_auth] C:\Users\<USER>\StudioProjects\aqua5_app\build\firebase_auth\intermediates\merged_manifest\debug\AndroidManifest.xml:9:13-11:85
89                android:name="com.google.firebase.components:io.flutter.plugins.firebase.auth.FlutterFirebaseAuthRegistrar"
89-->[:firebase_auth] C:\Users\<USER>\StudioProjects\aqua5_app\build\firebase_auth\intermediates\merged_manifest\debug\AndroidManifest.xml:10:17-124
90                android:value="com.google.firebase.components.ComponentRegistrar" />
90-->[:firebase_auth] C:\Users\<USER>\StudioProjects\aqua5_app\build\firebase_auth\intermediates\merged_manifest\debug\AndroidManifest.xml:11:17-82
91            <meta-data
91-->[:firebase_core] C:\Users\<USER>\StudioProjects\aqua5_app\build\firebase_core\intermediates\merged_manifest\debug\AndroidManifest.xml:9:13-11:85
92                android:name="com.google.firebase.components:io.flutter.plugins.firebase.core.FlutterFirebaseCoreRegistrar"
92-->[:firebase_core] C:\Users\<USER>\StudioProjects\aqua5_app\build\firebase_core\intermediates\merged_manifest\debug\AndroidManifest.xml:10:17-124
93                android:value="com.google.firebase.components.ComponentRegistrar" />
93-->[:firebase_core] C:\Users\<USER>\StudioProjects\aqua5_app\build\firebase_core\intermediates\merged_manifest\debug\AndroidManifest.xml:11:17-82
94            <meta-data
94-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\ebacd37c83e541758107c7e857abb2d5\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:69:13-71:85
95                android:name="com.google.firebase.components:com.google.firebase.auth.FirebaseAuthRegistrar"
95-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\ebacd37c83e541758107c7e857abb2d5\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:70:17-109
96                android:value="com.google.firebase.components.ComponentRegistrar" />
96-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\ebacd37c83e541758107c7e857abb2d5\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:71:17-82
97            <meta-data
97-->[com.google.firebase:firebase-firestore:25.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\ecb0a821ecd9a0194c504f02bd823d5b\transformed\jetified-firebase-firestore-25.1.3\AndroidManifest.xml:17:13-19:85
98                android:name="com.google.firebase.components:com.google.firebase.firestore.FirebaseFirestoreKtxRegistrar"
98-->[com.google.firebase:firebase-firestore:25.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\ecb0a821ecd9a0194c504f02bd823d5b\transformed\jetified-firebase-firestore-25.1.3\AndroidManifest.xml:18:17-122
99                android:value="com.google.firebase.components.ComponentRegistrar" />
99-->[com.google.firebase:firebase-firestore:25.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\ecb0a821ecd9a0194c504f02bd823d5b\transformed\jetified-firebase-firestore-25.1.3\AndroidManifest.xml:19:17-82
100            <meta-data
100-->[com.google.firebase:firebase-firestore:25.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\ecb0a821ecd9a0194c504f02bd823d5b\transformed\jetified-firebase-firestore-25.1.3\AndroidManifest.xml:20:13-22:85
101                android:name="com.google.firebase.components:com.google.firebase.firestore.FirestoreRegistrar"
101-->[com.google.firebase:firebase-firestore:25.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\ecb0a821ecd9a0194c504f02bd823d5b\transformed\jetified-firebase-firestore-25.1.3\AndroidManifest.xml:21:17-111
102                android:value="com.google.firebase.components.ComponentRegistrar" />
102-->[com.google.firebase:firebase-firestore:25.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\ecb0a821ecd9a0194c504f02bd823d5b\transformed\jetified-firebase-firestore-25.1.3\AndroidManifest.xml:22:17-82
103            <meta-data
103-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\44fe30ab51c511c3d2dafdd532238319\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
104                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
104-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\44fe30ab51c511c3d2dafdd532238319\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
105                android:value="com.google.firebase.components.ComponentRegistrar" />
105-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\44fe30ab51c511c3d2dafdd532238319\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
106            <meta-data
106-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\ae9174fb2605d2700770c56a341d2036\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
107                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
107-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\ae9174fb2605d2700770c56a341d2036\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:36:17-109
108                android:value="com.google.firebase.components.ComponentRegistrar" />
108-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\ae9174fb2605d2700770c56a341d2036\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:37:17-82
109        </service>
110
111        <activity
111-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\ebacd37c83e541758107c7e857abb2d5\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:29:9-46:20
112            android:name="com.google.firebase.auth.internal.GenericIdpActivity"
112-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\ebacd37c83e541758107c7e857abb2d5\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:30:13-80
113            android:excludeFromRecents="true"
113-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\ebacd37c83e541758107c7e857abb2d5\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:31:13-46
114            android:exported="true"
114-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\ebacd37c83e541758107c7e857abb2d5\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:32:13-36
115            android:launchMode="singleTask"
115-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\ebacd37c83e541758107c7e857abb2d5\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:33:13-44
116            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
116-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\ebacd37c83e541758107c7e857abb2d5\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:34:13-72
117            <intent-filter>
117-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\ebacd37c83e541758107c7e857abb2d5\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:35:13-45:29
118                <action android:name="android.intent.action.VIEW" />
118-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\ebacd37c83e541758107c7e857abb2d5\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:36:17-69
118-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\ebacd37c83e541758107c7e857abb2d5\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:36:25-66
119
120                <category android:name="android.intent.category.DEFAULT" />
120-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\ebacd37c83e541758107c7e857abb2d5\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:38:17-76
120-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\ebacd37c83e541758107c7e857abb2d5\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:38:27-73
121                <category android:name="android.intent.category.BROWSABLE" />
121-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\ebacd37c83e541758107c7e857abb2d5\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:39:17-78
121-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\ebacd37c83e541758107c7e857abb2d5\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:39:27-75
122
123                <data
123-->C:\Users\<USER>\StudioProjects\aqua5_app\android\app\src\main\AndroidManifest.xml:42:13-50
124                    android:host="firebase.auth"
125                    android:path="/"
126                    android:scheme="genericidp" />
127            </intent-filter>
128        </activity>
129        <activity
129-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\ebacd37c83e541758107c7e857abb2d5\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:47:9-64:20
130            android:name="com.google.firebase.auth.internal.RecaptchaActivity"
130-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\ebacd37c83e541758107c7e857abb2d5\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:48:13-79
131            android:excludeFromRecents="true"
131-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\ebacd37c83e541758107c7e857abb2d5\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:49:13-46
132            android:exported="true"
132-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\ebacd37c83e541758107c7e857abb2d5\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:50:13-36
133            android:launchMode="singleTask"
133-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\ebacd37c83e541758107c7e857abb2d5\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:51:13-44
134            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
134-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\ebacd37c83e541758107c7e857abb2d5\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:52:13-72
135            <intent-filter>
135-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\ebacd37c83e541758107c7e857abb2d5\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:53:13-63:29
136                <action android:name="android.intent.action.VIEW" />
136-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\ebacd37c83e541758107c7e857abb2d5\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:36:17-69
136-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\ebacd37c83e541758107c7e857abb2d5\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:36:25-66
137
138                <category android:name="android.intent.category.DEFAULT" />
138-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\ebacd37c83e541758107c7e857abb2d5\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:38:17-76
138-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\ebacd37c83e541758107c7e857abb2d5\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:38:27-73
139                <category android:name="android.intent.category.BROWSABLE" />
139-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\ebacd37c83e541758107c7e857abb2d5\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:39:17-78
139-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\ebacd37c83e541758107c7e857abb2d5\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:39:27-75
140
141                <data
141-->C:\Users\<USER>\StudioProjects\aqua5_app\android\app\src\main\AndroidManifest.xml:42:13-50
142                    android:host="firebase.auth"
143                    android:path="/"
144                    android:scheme="recaptcha" />
145            </intent-filter>
146        </activity>
147
148        <provider
148-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\ae9174fb2605d2700770c56a341d2036\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
149            android:name="com.google.firebase.provider.FirebaseInitProvider"
149-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\ae9174fb2605d2700770c56a341d2036\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:24:13-77
150            android:authorities="com.example.aqua5_app.firebaseinitprovider"
150-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\ae9174fb2605d2700770c56a341d2036\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:25:13-72
151            android:directBootAware="true"
151-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\ae9174fb2605d2700770c56a341d2036\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:26:13-43
152            android:exported="false"
152-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\ae9174fb2605d2700770c56a341d2036\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:27:13-37
153            android:initOrder="100" />
153-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\ae9174fb2605d2700770c56a341d2036\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:28:13-36
154
155        <service
155-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\d1203d5e6c60912004b024317ed93069\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:24:9-32:19
156            android:name="androidx.credentials.playservices.CredentialProviderMetadataHolder"
156-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\d1203d5e6c60912004b024317ed93069\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:25:13-94
157            android:enabled="true"
157-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\d1203d5e6c60912004b024317ed93069\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:26:13-35
158            android:exported="false" >
158-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\d1203d5e6c60912004b024317ed93069\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:27:13-37
159            <meta-data
159-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\d1203d5e6c60912004b024317ed93069\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:29:13-31:104
160                android:name="androidx.credentials.CREDENTIAL_PROVIDER_KEY"
160-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\d1203d5e6c60912004b024317ed93069\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:30:17-76
161                android:value="androidx.credentials.playservices.CredentialProviderPlayServicesImpl" />
161-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\d1203d5e6c60912004b024317ed93069\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:31:17-101
162        </service>
163
164        <activity
164-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\d1203d5e6c60912004b024317ed93069\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:34:9-41:20
165            android:name="androidx.credentials.playservices.HiddenActivity"
165-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\d1203d5e6c60912004b024317ed93069\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:35:13-76
166            android:configChanges="orientation|screenSize|screenLayout|keyboardHidden"
166-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\d1203d5e6c60912004b024317ed93069\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:36:13-87
167            android:enabled="true"
167-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\d1203d5e6c60912004b024317ed93069\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:37:13-35
168            android:exported="false"
168-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\d1203d5e6c60912004b024317ed93069\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:38:13-37
169            android:fitsSystemWindows="true"
169-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\d1203d5e6c60912004b024317ed93069\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:39:13-45
170            android:theme="@style/Theme.Hidden" >
170-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\d1203d5e6c60912004b024317ed93069\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:40:13-48
171        </activity>
172        <activity
172-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ef37e14ee12f7e0d24dd6db72203879d\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:23:9-27:75
173            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
173-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ef37e14ee12f7e0d24dd6db72203879d\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:24:13-93
174            android:excludeFromRecents="true"
174-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ef37e14ee12f7e0d24dd6db72203879d\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:25:13-46
175            android:exported="false"
175-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ef37e14ee12f7e0d24dd6db72203879d\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:26:13-37
176            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
176-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ef37e14ee12f7e0d24dd6db72203879d\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:27:13-72
177        <!--
178            Service handling Google Sign-In user revocation. For apps that do not integrate with
179            Google Sign-In, this service will never be started.
180        -->
181        <service
181-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ef37e14ee12f7e0d24dd6db72203879d\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:33:9-37:51
182            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
182-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ef37e14ee12f7e0d24dd6db72203879d\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:34:13-89
183            android:exported="true"
183-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ef37e14ee12f7e0d24dd6db72203879d\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:35:13-36
184            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
184-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ef37e14ee12f7e0d24dd6db72203879d\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:36:13-107
185            android:visibleToInstantApps="true" />
185-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ef37e14ee12f7e0d24dd6db72203879d\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:37:13-48
186
187        <activity
187-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c84e8a146159499b9c85f9e0df490157\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:20:9-22:45
188            android:name="com.google.android.gms.common.api.GoogleApiActivity"
188-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c84e8a146159499b9c85f9e0df490157\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:20:19-85
189            android:exported="false"
189-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c84e8a146159499b9c85f9e0df490157\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:22:19-43
190            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
190-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c84e8a146159499b9c85f9e0df490157\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:21:19-78
191
192        <uses-library
192-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\f8ec00f3fcc9d3a9a9c66e73a873b76c\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
193            android:name="androidx.window.extensions"
193-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\f8ec00f3fcc9d3a9a9c66e73a873b76c\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
194            android:required="false" />
194-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\f8ec00f3fcc9d3a9a9c66e73a873b76c\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
195        <uses-library
195-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\f8ec00f3fcc9d3a9a9c66e73a873b76c\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
196            android:name="androidx.window.sidecar"
196-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\f8ec00f3fcc9d3a9a9c66e73a873b76c\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
197            android:required="false" />
197-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\f8ec00f3fcc9d3a9a9c66e73a873b76c\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
198
199        <meta-data
199-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\ab921d2504dc4ffdb3a2cd4464883cfe\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:9-122
200            android:name="com.google.android.gms.version"
200-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\ab921d2504dc4ffdb3a2cd4464883cfe\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:20-65
201            android:value="@integer/google_play_services_version" />
201-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\ab921d2504dc4ffdb3a2cd4464883cfe\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:66-119
202
203        <provider
203-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\2cde72fe132e920ad7a2e2d1624dc792\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
204            android:name="androidx.startup.InitializationProvider"
204-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\2cde72fe132e920ad7a2e2d1624dc792\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:25:13-67
205            android:authorities="com.example.aqua5_app.androidx-startup"
205-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\2cde72fe132e920ad7a2e2d1624dc792\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:26:13-68
206            android:exported="false" >
206-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\2cde72fe132e920ad7a2e2d1624dc792\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:27:13-37
207            <meta-data
207-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\2cde72fe132e920ad7a2e2d1624dc792\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
208                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
208-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\2cde72fe132e920ad7a2e2d1624dc792\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
209                android:value="androidx.startup" />
209-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\2cde72fe132e920ad7a2e2d1624dc792\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
210            <meta-data
210-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e080b70ddc4c24ec0f58ebf52f1f6b5c\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
211                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
211-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e080b70ddc4c24ec0f58ebf52f1f6b5c\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
212                android:value="androidx.startup" />
212-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e080b70ddc4c24ec0f58ebf52f1f6b5c\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
213        </provider>
214
215        <receiver
215-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e080b70ddc4c24ec0f58ebf52f1f6b5c\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
216            android:name="androidx.profileinstaller.ProfileInstallReceiver"
216-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e080b70ddc4c24ec0f58ebf52f1f6b5c\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
217            android:directBootAware="false"
217-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e080b70ddc4c24ec0f58ebf52f1f6b5c\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
218            android:enabled="true"
218-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e080b70ddc4c24ec0f58ebf52f1f6b5c\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
219            android:exported="true"
219-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e080b70ddc4c24ec0f58ebf52f1f6b5c\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
220            android:permission="android.permission.DUMP" >
220-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e080b70ddc4c24ec0f58ebf52f1f6b5c\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
221            <intent-filter>
221-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e080b70ddc4c24ec0f58ebf52f1f6b5c\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
222                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
222-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e080b70ddc4c24ec0f58ebf52f1f6b5c\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
222-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e080b70ddc4c24ec0f58ebf52f1f6b5c\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
223            </intent-filter>
224            <intent-filter>
224-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e080b70ddc4c24ec0f58ebf52f1f6b5c\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
225                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
225-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e080b70ddc4c24ec0f58ebf52f1f6b5c\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
225-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e080b70ddc4c24ec0f58ebf52f1f6b5c\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
226            </intent-filter>
227            <intent-filter>
227-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e080b70ddc4c24ec0f58ebf52f1f6b5c\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
228                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
228-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e080b70ddc4c24ec0f58ebf52f1f6b5c\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
228-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e080b70ddc4c24ec0f58ebf52f1f6b5c\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
229            </intent-filter>
230            <intent-filter>
230-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e080b70ddc4c24ec0f58ebf52f1f6b5c\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
231                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
231-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e080b70ddc4c24ec0f58ebf52f1f6b5c\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
231-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e080b70ddc4c24ec0f58ebf52f1f6b5c\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
232            </intent-filter>
233        </receiver> <!-- The activities will be merged into the manifest of the hosting app. -->
234        <activity
234-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\transforms-3\22fae1222ad5adc939f1d612ad3b937f\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:14:9-18:65
235            android:name="com.google.android.play.core.common.PlayCoreDialogWrapperActivity"
235-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\transforms-3\22fae1222ad5adc939f1d612ad3b937f\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:15:13-93
236            android:exported="false"
236-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\transforms-3\22fae1222ad5adc939f1d612ad3b937f\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:16:13-37
237            android:stateNotNeeded="true"
237-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\transforms-3\22fae1222ad5adc939f1d612ad3b937f\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:17:13-42
238            android:theme="@style/Theme.PlayCore.Transparent" />
238-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\transforms-3\22fae1222ad5adc939f1d612ad3b937f\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:18:13-62
239    </application>
240
241</manifest>
