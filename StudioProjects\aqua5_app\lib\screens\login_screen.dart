import 'package:aqua5_app/screens/password_reset_screen.dart';
import 'package:aqua5_app/screens/signup_screen.dart';
import 'package:aqua5_app/screens/otp_verification_screen.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../app_state.dart';
import '../colors.dart';
import '../services/auth_service.dart';
import '../utils/validators.dart';
import '../widgets/recaptcha_verification.dart';

class LoginScreen extends StatefulWidget {
  const LoginScreen({super.key});

  @override
  State<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen> {
  final _formKey = GlobalKey<FormState>();
  final _emailOrPhoneController = TextEditingController();
  final _passwordController = TextEditingController();
  bool _isEmailLogin = true;
  bool _isLoading = false;
  String? _errorMessage;
  final _authService = AuthService();

  // State for reCAPTCHA verification
  bool _showRecaptcha = false;

  // Firebase reCAPTCHA site key - replace with your actual site key
  // This is a placeholder and should be replaced with your actual site key
  final String _recaptchaSiteKey = '6LfF4DYrAAAAAPNbTE-27Rofdmeym5madH4Nu_Xf';

  @override
  void dispose() {
    _emailOrPhoneController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  // Handle email/password login
  Future<void> _handleEmailLogin() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    final result = await _authService.signInWithEmail(
      _emailOrPhoneController.text.trim(),
      _passwordController.text,
    );

    setState(() {
      _isLoading = false;
    });

    if (result.success) {
      // Login successful, navigation will be handled by AuthWrapper
    } else {
      setState(() {
        _errorMessage = result.errorMessage;
      });
    }
  }

  // Handle phone login (show reCAPTCHA first)
  void _handlePhoneLogin() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    // Show reCAPTCHA verification
    setState(() {
      _showRecaptcha = true;
      _errorMessage = null;
    });
  }

  // Handle reCAPTCHA verification result
  void _onRecaptchaVerified(bool success, String? error) {
    if (success) {
      setState(() {
        _showRecaptcha = false;
      });
      _sendOTP();
    } else {
      setState(() {
        _showRecaptcha = false;
        _errorMessage = error ?? 'reCAPTCHA verification failed';
      });
    }
  }

  // Send OTP after reCAPTCHA verification
  Future<void> _sendOTP() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    final result = await _authService.sendOTP(
      _emailOrPhoneController.text.trim(),
      context: context,
    );

    setState(() {
      _isLoading = false;
    });

    if (result.success) {
      // Navigate to OTP verification screen
      if (mounted) {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => OtpVerificationScreen(
              phoneNumber: _emailOrPhoneController.text.trim(),
              verificationId: result.errorMessage ??
                  '', // Using errorMessage to pass verificationId
            ),
          ),
        );
      }
    } else {
      setState(() {
        _errorMessage = result.errorMessage;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
        child: Scaffold(
      backgroundColor: AppColors.background,
      body: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Form(
          key: _formKey,
          child: Consumer<AppState>(
            builder: (context, appState, child) {
              return SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const SizedBox(height: 40),
                    Text(
                      'Welcome Back',
                      style: TextStyle(
                        fontWeight: FontWeight.w800,
                        fontSize: 28,
                        color: const Color.fromARGB(255, 0, 0, 0),
                        letterSpacing: 0.5,
                      ),
                    ),
                    Text(
                      'Log in to continue',
                      style: TextStyle(
                        fontWeight: FontWeight.w500,
                        fontSize: 14,
                        color: const Color.fromARGB(255, 54, 54, 54)
                            .withAlpha(178),
                      ),
                    ),
                    const SizedBox(height: 32),
                    if (_showRecaptcha)
                      // Show reCAPTCHA verification widget
                      Container(
                        width: double.infinity,
                        height: 400,
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(16),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withAlpha(13),
                              blurRadius: 10,
                              spreadRadius: 0,
                              offset: const Offset(0, 5),
                            ),
                          ],
                        ),
                        child: ClipRRect(
                          borderRadius: BorderRadius.circular(16),
                          child: RecaptchaVerification(
                            siteKey: _recaptchaSiteKey,
                            onVerificationComplete: _onRecaptchaVerified,
                          ),
                        ),
                      )
                    else
                      Container(
                        width: double.infinity,
                        decoration: BoxDecoration(
                          color: AppColors.cardBackground,
                          borderRadius: BorderRadius.circular(16),
                          boxShadow: [
                            // BoxShadow(
                            //   color: Colors.black.withOpacity(0.1),
                            //   blurRadius: 10,
                            //   offset: const Offset(0, 4),
                            // ),
                          ],
                        ),
                        child: Padding(
                          padding: const EdgeInsets.all(20.0),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  GestureDetector(
                                    onTap: () {
                                      setState(() {
                                        _isEmailLogin = true;
                                      });
                                    },
                                    child: Text(
                                      'Email',
                                      style: TextStyle(
                                        fontSize: 16,
                                        color: _isEmailLogin
                                            ? Colors.blue
                                            : Colors.blue.withAlpha(128),
                                        fontWeight: FontWeight.w600,
                                      ),
                                    ),
                                  ),
                                  GestureDetector(
                                    onTap: () {
                                      setState(() {
                                        _isEmailLogin = false;
                                      });
                                    },
                                    child: Text(
                                      'Phone',
                                      style: TextStyle(
                                        fontSize: 16,
                                        color: !_isEmailLogin
                                            ? Colors.blue
                                            : Colors.blue.withAlpha(128),
                                        fontWeight: FontWeight.w600,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                              const SizedBox(height: 20),
                              TextFormField(
                                controller: _emailOrPhoneController,
                                decoration: InputDecoration(
                                  labelText:
                                      _isEmailLogin ? 'Email' : 'Phone Number',
                                  labelStyle: TextStyle(
                                    color:
                                        const Color.fromARGB(255, 11, 83, 143)
                                            .withAlpha(178),
                                  ),
                                  filled: true,
                                  fillColor: AppColors.accent.withAlpha(25),
                                  border: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(12),
                                    borderSide: BorderSide.none,
                                  ),
                                  errorStyle: TextStyle(color: Colors.red),
                                ),
                                style: TextStyle(
                                  color: const Color.fromARGB(255, 0, 0, 0),
                                  fontSize: 14,
                                ),
                                keyboardType: _isEmailLogin
                                    ? TextInputType.emailAddress
                                    : TextInputType.phone,
                                validator: _isEmailLogin
                                    ? Validators.validateEmail
                                    : Validators.validatePhone,
                              ),
                              const SizedBox(height: 16),
                              if (_isEmailLogin)
                                TextFormField(
                                  controller: _passwordController,
                                  decoration: InputDecoration(
                                    labelText: 'Password',
                                    labelStyle: TextStyle(
                                      color:
                                          const Color.fromARGB(255, 11, 83, 143)
                                              .withAlpha(178),
                                    ),
                                    filled: true,
                                    fillColor: AppColors.accent.withAlpha(25),
                                    border: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(12),
                                      borderSide: BorderSide.none,
                                    ),
                                    errorStyle: TextStyle(color: Colors.red),
                                  ),
                                  style: TextStyle(
                                    color: Colors.black,
                                    fontSize: 14,
                                  ),
                                  obscureText: true,
                                  validator: Validators.validatePassword,
                                ),
                              if (_errorMessage != null)
                                Padding(
                                  padding: const EdgeInsets.only(top: 8.0),
                                  child: Text(
                                    _errorMessage!,
                                    style: TextStyle(
                                      color: Colors.red,
                                      fontSize: 14,
                                    ),
                                  ),
                                ),
                              const SizedBox(height: 16),
                              if (_isEmailLogin)
                                Align(
                                  alignment: Alignment.centerRight,
                                  child: TextButton(
                                    onPressed: () {
                                      Navigator.push(
                                        context,
                                        MaterialPageRoute(
                                          builder: (context) =>
                                              const PasswordResetScreen(),
                                        ),
                                      );
                                    },
                                    child: Text(
                                      'Forgot Password?',
                                      style: TextStyle(
                                        color: AppColors.accent,
                                        fontSize: 14,
                                        fontWeight: FontWeight.w600,
                                      ),
                                    ),
                                  ),
                                ),
                              const SizedBox(height: 24),
                              ElevatedButton(
                                style: ElevatedButton.styleFrom(
                                  minimumSize: const Size.fromHeight(52),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                  backgroundColor: AppColors.accent,
                                  foregroundColor: Colors.white,
                                  elevation: 2,
                                ),
                                onPressed: _isLoading
                                    ? null
                                    : _isEmailLogin
                                        ? _handleEmailLogin
                                        : _handlePhoneLogin,
                                child: _isLoading
                                    ? const CircularProgressIndicator(
                                        color: Colors.white)
                                    : Text(
                                        _isEmailLogin ? 'Log In' : 'Send OTP',
                                        style: TextStyle(
                                          fontSize: 16,
                                          fontWeight: FontWeight.w600,
                                        ),
                                      ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    const SizedBox(height: 24),
                    Center(
                      child: TextButton(
                        onPressed: () {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                                builder: (context) => const SignupScreen()),
                          );
                        },
                        child: Text.rich(
                          TextSpan(
                            children: [
                              TextSpan(
                                text: 'Don’t have an account? ',
                                style: TextStyle(
                                  color: const Color.fromARGB(255, 87, 87, 87)
                                      .withAlpha(178),
                                  fontSize: 14,
                                ),
                              ),
                              TextSpan(
                                text: 'Sign Up',
                                style: TextStyle(
                                  color: AppColors.accent,
                                  fontSize: 14,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              );
            },
          ),
        ),
      ),
    ));
  }
}
