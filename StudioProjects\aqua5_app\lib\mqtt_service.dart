import 'package:aqua5_app/constance.dart';
import 'package:mqtt_client/mqtt_client.dart';
import 'package:mqtt_client/mqtt_server_client.dart';

class MQTTService {
  late MqttServerClient client;
  final String clientId;
  Function(String, String)? onMessage;

  MQTTService(this.clientId) {
    client = MqttServerClient(AppConstants.mqttBroker, clientId);
    client.port = AppConstants.mqttPort;
    client.logging(on: true);
    client.onDisconnected = _onDisconnected;
    client.onConnected = _onConnected;
    client.onSubscribed = _onSubscribed;
  }

  Future<void> connect() async {
    try {
      await client.connect();
    } catch (e) {
      print('MQTT Connection failed: $e');
      client.disconnect();
    }
  }

  void _onConnected() {
    print('MQTT Connected');
    client.subscribe('device/+/announce', MqttQos.atLeastOnce);
    client.subscribe('device/+/status', MqttQos.atLeastOnce);
    client.updates!.listen((List<MqttReceivedMessage<MqttMessage>> c) {
      final message = c[0].payload as MqttPublishMessage;
      final payload =
          MqttPublishPayload.bytesToStringAsString(message.payload.message);
      onMessage?.call(c[0].topic, payload);
    });
  }

  void _onDisconnected() {
    print('MQTT Disconnected');
  }

  void _onSubscribed(String topic) {
    print('Subscribed to $topic');
  }

  void publish(String topic, String message) {
    final builder = MqttClientPayloadBuilder();
    builder.addString(message);
    client.publishMessage(topic, MqttQos.atLeastOnce, builder.payload!);
  }

  void disconnect() {
    client.disconnect();
  }
}
