import 'package:aqua5_app/screens/widgets/custom_switch.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:provider/provider.dart';
import '../app_state.dart';
import '../colors.dart';

class HomeScreen extends StatelessWidget {
  const HomeScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<AppState>(
      builder: (context, appState, child) {
        return SingleChildScrollView(
          padding: const EdgeInsets.all(18.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Hello, Monzer',
                        style: TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 24,
                            color: Colors.black),
                      ),
                      Text(
                        'Wednesday, 30 Apr',
                        style: TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 12,
                            color: const Color.fromARGB(223, 0, 0, 0)),
                      ),
                    ],
                  ),
                  CircleAvatar(
                    radius: 25,
                    backgroundColor: AppColors.accent.withValues(alpha: 51),
                    child: Icon(
                      CupertinoIcons.person,
                      size: 25,
                      color: AppColors.accent,
                    ),
                  )
                ],
              ),
              SizedBox(
                height: 24,
              ),
              Container(
                width: double.infinity,
                decoration: BoxDecoration(
                    color: AppColors.cardBackground,
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: AppColors.borderColor)),
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          const Text('TDS Level',
                              style: TextStyle(
                                  fontSize: 18,
                                  color: Color.fromARGB(255, 0, 0, 0),
                                  fontWeight: FontWeight.w400)),
                          Container(
                            padding: EdgeInsets.symmetric(
                                horizontal: 16, vertical: 4),
                            decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(25),
                                color: AppColors.accent.withValues(alpha: 77)),
                            child: const Text('Safe',
                                style: TextStyle(
                                    fontSize: 14, color: AppColors.accent)),
                          ),
                        ],
                      ),
                      SizedBox(
                        height: 12,
                      ),
                      Text.rich(TextSpan(children: [
                        TextSpan(
                          text: '${appState.tdsLevel} ',
                          style: TextStyle(
                              fontSize: 32,
                              fontWeight: FontWeight.w900,
                              color: Color.fromARGB(255, 0, 0, 0)),
                        ),
                        TextSpan(
                          text: 'ppm',
                          style: const TextStyle(
                              fontSize: 18,
                              color: Color.fromARGB(223, 0, 0, 0)),
                        ),
                      ])),
                      Row(
                        children: [
                          Spacer(),
                          Icon(
                            FontAwesomeIcons.arrowsRotate,
                            size: 18,
                            color: AppColors.accent,
                          ),
                          SizedBox(
                            width: 8,
                          ),
                          Text('Live')
                        ],
                      )
                    ],
                  ),
                ),
              ),
              SizedBox(
                height: 24,
              ),
              Container(
                width: double.infinity,
                decoration: BoxDecoration(
                    color: AppColors.cardBackground,
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: AppColors.borderColor)),
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text('Water Purifier',
                          style: TextStyle(
                              fontSize: 18,
                              color: Color.fromARGB(255, 0, 0, 0),
                              fontWeight: FontWeight.w400)),
                      SizedBox(
                        height: 30,
                      ),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          CustomSwitch(
                            isOn: true,
                            isLoading: false,
                            onChanged: (value) {},
                          ),
                        ],
                      ),
                      SizedBox(
                        height: 30,
                      ),
                    ],
                  ),
                ),
              ),
              SizedBox(
                height: 24,
              ),
              Container(
                width: double.infinity,
                decoration: BoxDecoration(
                    color: AppColors.cardBackground,
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: AppColors.borderColor)),
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          const Text('Daily Usage',
                              style: TextStyle(
                                  fontSize: 18,
                                  color: Color.fromARGB(255, 0, 0, 0),
                                  fontWeight: FontWeight.w400)),
                          Container(
                            padding: EdgeInsets.symmetric(
                                horizontal: 16, vertical: 4),
                            child: const Text('Today',
                                style: TextStyle(
                                  fontSize: 14,
                                )),
                          ),
                        ],
                      ),
                      SizedBox(
                        height: 12,
                      ),
                      Text.rich(TextSpan(children: [
                        TextSpan(
                          text: '${appState.dailyUsage} ',
                          style: TextStyle(
                              fontSize: 32,
                              fontWeight: FontWeight.w900,
                              color: Color.fromARGB(255, 0, 0, 0)),
                        ),
                        TextSpan(
                          text: 'L of ${appState.quota}L Quota',
                          style: const TextStyle(
                              fontSize: 18,
                              color: Color.fromARGB(223, 0, 0, 0)),
                        ),
                      ])),
                      SizedBox(
                        height: 16,
                      ),
                      LinearProgressIndicator(
                        value: 0.8,
                        color: AppColors.accent,
                        backgroundColor: AppColors.accent.withValues(alpha: 51),
                        minHeight: 12,
                        borderRadius: BorderRadius.circular(22),
                      ),
                      SizedBox(
                        height: 24,
                      ),
                      SizedBox(
                        height: 100,
                        child: Padding(
                          padding: const EdgeInsets.all(8.0),
                          child: LineChart(
                            LineChartData(
                              gridData: FlGridData(show: false),
                              titlesData: FlTitlesData(show: false),
                              borderData: FlBorderData(show: false),
                              lineBarsData: [
                                LineChartBarData(
                                  spots: [
                                    FlSpot(0, 1),
                                    FlSpot(1, 1.5),
                                    FlSpot(2, 1.4),
                                    FlSpot(3, 3.4),
                                    FlSpot(4, 2),
                                    FlSpot(5, 2.2),
                                    FlSpot(6, 1.8),
                                  ],
                                  isCurved: true,
                                  color: AppColors.accent,
                                  barWidth: 3,
                                  isStrokeCapRound: true,
                                  belowBarData: BarAreaData(
                                      show: true,
                                      color: AppColors.accent
                                          .withValues(alpha: 51)),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                      SizedBox(
                        height: 16,
                      ),
                      Row(
                        children: [
                          Text(
                            'Monthly',
                          ),
                          Spacer(),
                          InkWell(
                            onTap: () {},
                            child: Text(
                              'View History',
                              style: TextStyle(color: AppColors.accent),
                            ),
                          )
                        ],
                      )
                    ],
                  ),
                ),
              ),
              SizedBox(
                height: 24,
              ),
              Container(
                width: double.infinity,
                decoration: BoxDecoration(
                    color: AppColors.cardBackground,
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: AppColors.borderColor)),
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text('Connection Health',
                          style: TextStyle(
                              fontSize: 18,
                              color: Color.fromARGB(255, 0, 0, 0),
                              fontWeight: FontWeight.w400)),
                      SizedBox(
                        height: 12,
                      ),
                      Row(
                        spacing: 16,
                        children: [
                          CircleAvatar(
                            radius: 25,
                            backgroundColor:
                                AppColors.accent.withValues(alpha: 51),
                            child: Icon(
                              CupertinoIcons.wifi,
                              size: 20,
                              color: AppColors.accent,
                            ),
                          ),
                          Text('Connected',
                              style: TextStyle(
                                  fontSize: 24,
                                  color: Color.fromARGB(255, 0, 0, 0),
                                  fontWeight: FontWeight.w700)),
                        ],
                      ),
                      SizedBox(
                        height: 16,
                      ),
                    ],
                  ),
                ),
              ),
              SizedBox(
                height: 24,
              ),
              Container(
                width: double.infinity,
                decoration: BoxDecoration(
                    color: AppColors.cardBackground,
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: AppColors.borderColor)),
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text('Filter Status',
                          style: TextStyle(
                              fontSize: 18,
                              color: Color.fromARGB(255, 0, 0, 0),
                              fontWeight: FontWeight.w400)),
                      SizedBox(
                        height: 12,
                      ),
                      Row(
                        children: [
                          CircleAvatar(
                            radius: 25,
                            backgroundColor:
                                AppColors.accent.withValues(alpha: 51),
                            child: Icon(
                              FontAwesomeIcons.lockOpen,
                              size: 20,
                              color: AppColors.accent,
                            ),
                          ),
                          SizedBox(
                            width: 16,
                          ),
                          Text(appState.isFilterActive ? 'Active' : 'Locked',
                              style: TextStyle(
                                  fontSize: 24,
                                  color: Color.fromARGB(255, 0, 0, 0),
                                  fontWeight: FontWeight.w400)),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
              SizedBox(
                height: 24,
              ),
              Container(
                width: double.infinity,
                decoration: BoxDecoration(
                    color: AppColors.cardBackground,
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: AppColors.borderColor)),
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          const Text('Current Plan',
                              style: TextStyle(
                                  fontSize: 18,
                                  color: Color.fromARGB(255, 0, 0, 0),
                                  fontWeight: FontWeight.w400)),
                          Container(
                            padding: EdgeInsets.symmetric(
                                horizontal: 16, vertical: 4),
                            decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(25),
                                color: AppColors.accent.withValues(alpha: 77)),
                            child: const Text('Premium',
                                style: TextStyle(
                                    fontSize: 14, color: AppColors.accent)),
                          ),
                        ],
                      ),
                      SizedBox(
                        height: 12,
                      ),
                      Text.rich(TextSpan(children: [
                        TextSpan(
                          text: 'Premium Plan',
                          style: TextStyle(
                              fontSize: 24,
                              fontWeight: FontWeight.w900,
                              color: Color.fromARGB(255, 0, 0, 0)),
                        ),
                      ])),
                      Text('Valid Until: 30/5/2025',
                          style: TextStyle(
                            fontSize: 14,
                          )),
                      SizedBox(
                        height: 16,
                      ),
                      Row(
                        children: [
                          Expanded(
                            child: LinearProgressIndicator(
                              value: 0.8,
                              color: AppColors.accent,
                              backgroundColor:
                                  AppColors.accent.withValues(alpha: 51),
                              minHeight: 12,
                              borderRadius: BorderRadius.circular(22),
                            ),
                          ),
                          SizedBox(
                            width: 12,
                          ),
                          Text('7 Days left',
                              style: TextStyle(
                                fontSize: 14,
                              )),
                        ],
                      ),
                      SizedBox(
                        height: 24,
                      ),
                      ElevatedButton(
                        style: ElevatedButton.styleFrom(
                          minimumSize: const Size.fromHeight(50),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                        onPressed: () {},
                        child: const Text('Renew / Upgrade'),
                      )
                    ],
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
