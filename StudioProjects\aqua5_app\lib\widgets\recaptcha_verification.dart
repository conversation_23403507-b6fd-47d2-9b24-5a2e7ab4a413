import 'package:flutter/material.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'dart:async';
import 'package:webview_flutter/webview_flutter.dart';
import 'package:flutter/foundation.dart' show kIsWeb;

/// A widget that handles reCAPTCHA verification for Firebase Phone Authentication
class RecaptchaVerification extends StatefulWidget {
  final Function(bool success, String? error) onVerificationComplete;
  final String siteKey;

  const RecaptchaVerification({
    Key? key,
    required this.onVerificationComplete,
    required this.siteKey,
  }) : super(key: key);

  @override
  State<RecaptchaVerification> createState() => _RecaptchaVerificationState();
}

class _RecaptchaVerificationState extends State<RecaptchaVerification> {
  bool _isLoading = true;
  String? _errorMessage;
  late WebViewController _webViewController;
  final Completer<void> _recaptchaCompleter = Completer<void>();

  @override
  void initState() {
    super.initState();
    if (!kIsWeb) {
      _initWebView();
    }
  }

  void _initWebView() {
    _webViewController = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setBackgroundColor(Colors.white)
      ..setNavigationDelegate(
        NavigationDelegate(
          onPageStarted: (String url) {
            setState(() {
              _isLoading = true;
            });
          },
          onPageFinished: (String url) {
            setState(() {
              _isLoading = false;
            });
            _injectRecaptchaScript();
          },
          onWebResourceError: (WebResourceError error) {
            setState(() {
              _errorMessage = 'Failed to load reCAPTCHA: ${error.description}';
              _isLoading = false;
            });
            widget.onVerificationComplete(false, _errorMessage);
          },
        ),
      )
      ..loadHtmlString(_buildRecaptchaHtml());
  }

  String _buildRecaptchaHtml() {
    return '''
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <script src="https://www.google.com/recaptcha/api.js" async defer></script>
        <style>
          body, html {
            margin: 0;
            padding: 0;
            width: 100%;
            height: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            background-color: #f9f9f9;
          }
          .container {
            display: flex;
            flex-direction: column;
            align-items: center;
          }
          .title {
            font-family: Arial, sans-serif;
            font-size: 16px;
            color: #333;
            margin-bottom: 20px;
            text-align: center;
          }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="title">Please complete the verification</div>
          <div class="g-recaptcha" 
               data-sitekey="${widget.siteKey}" 
               data-callback="onRecaptchaVerified"></div>
        </div>
        <script>
          function onRecaptchaVerified(token) {
            // Send the token back to Flutter
            if (window.flutter_inappwebview) {
              window.flutter_inappwebview.callHandler('onRecaptchaVerified', token);
            } else {
              // For webview_flutter
              RecaptchaVerifiedCallback.postMessage(token);
            }
          }
        </script>
      </body>
      </html>
    ''';
  }

  void _injectRecaptchaScript() {
    _webViewController.addJavaScriptChannel(
      'RecaptchaVerifiedCallback',
      onMessageReceived: (JavaScriptMessage message) {
        // Handle the reCAPTCHA token
        final token = message.message;
        if (token.isNotEmpty) {
          widget.onVerificationComplete(true, null);
          if (!_recaptchaCompleter.isCompleted) {
            _recaptchaCompleter.complete();
          }
        } else {
          widget.onVerificationComplete(false, 'reCAPTCHA verification failed');
        }
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    if (kIsWeb) {
      // For web, we'll use the Firebase Web SDK's built-in reCAPTCHA
      return Center(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              'Please complete the reCAPTCHA verification',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
            ),
            const SizedBox(height: 20),
            // The actual reCAPTCHA will be rendered by Firebase Web SDK
            Container(
              width: 300,
              height: 100,
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey.shade300),
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Center(
                child: Text('reCAPTCHA will appear here'),
              ),
            ),
          ],
        ),
      );
    }

    return Stack(
      children: [
        WebViewWidget(controller: _webViewController),
        if (_isLoading)
          const Center(
            child: CircularProgressIndicator(),
          ),
        if (_errorMessage != null)
          Center(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Icon(Icons.error_outline, color: Colors.red, size: 48),
                  const SizedBox(height: 16),
                  Text(
                    _errorMessage!,
                    style: const TextStyle(color: Colors.red),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () {
                      setState(() {
                        _errorMessage = null;
                        _isLoading = true;
                      });
                      _webViewController.reload();
                    },
                    child: const Text('Retry'),
                  ),
                ],
              ),
            ),
          ),
      ],
    );
  }
}
