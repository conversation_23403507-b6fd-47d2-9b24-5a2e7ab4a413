import 'package:aqua5_app/app_drawer.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../app_state.dart';
import '../colors.dart';

class DeviceBindingScreen extends StatefulWidget {
  const DeviceBindingScreen({super.key});

  @override
  State<DeviceBindingScreen> createState() => _DeviceBindingScreenState();
}

class _DeviceBindingScreenState extends State<DeviceBindingScreen> {
  final TextEditingController _deviceIdController = TextEditingController();

  @override
  void dispose() {
    _deviceIdController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Device Binding'),
        backgroundColor: AppColors.cardBackground,
      ),
      drawer: const AppDrawer(),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('Bind a New Device', style: TextStyle(fontSize: 20)),
            const SizedBox(height: 16),
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  children: [
                    TextField(
                      controller: _deviceIdController,
                      decoration: const InputDecoration(
                        labelText: 'Device ID',
                        border: OutlineInputBorder(),
                      ),
                    ),
                    const SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: () {
                        final deviceId = _deviceIdController.text.trim();
                        if (deviceId.isNotEmpty) {
                          context.read<AppState>().bindDevice(deviceId);
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(
                                content: Text('Device bound successfully!')),
                          );
                          _deviceIdController.clear();
                        }
                      },
                      child: const Text('Bind Device'),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
            const Text('Bound Devices', style: TextStyle(fontSize: 20)),
            const SizedBox(height: 8),
            Consumer<AppState>(
              builder: (context, appState, child) {
                final userDevices = appState.devices
                    .where(
                        (device) => device['user'].contains(appState.userEmail))
                    .toList();
                return ListView.builder(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  itemCount: userDevices.length,
                  itemBuilder: (context, index) {
                    final device = userDevices[index];
                    return Card(
                      child: ListTile(
                        title: Text(device['name']),
                        subtitle: Text(
                            'ID: ${device['id']} | Status: ${device['status']}'),
                      ),
                    );
                  },
                );
              },
            ),
          ],
        ),
      ),
    );
  }
}
