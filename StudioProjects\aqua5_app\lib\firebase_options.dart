// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;

      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyA3TMK5LYJJl8bDkR7QQMtxkFpDWaGmNYc',
    appId: '1:617002149493:android:1dac630a0f3411bd823a3c',
    messagingSenderId: '617002149493',
    projectId: 'acqua5',
    storageBucket: 'acqua5.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyDd7Mk3qjBEfR9hE8rgBCjXtVAKJV_F6OM',
    appId: '1:617002149493:ios:a9399d48e5c3e3be823a3c',
    messagingSenderId: '617002149493',
    projectId: 'acqua5',
    storageBucket: 'acqua5.firebasestorage.app',
    iosBundleId: 'com.example.aqua5App',
  );
}
