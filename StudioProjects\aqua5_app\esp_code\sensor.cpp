#include <Arduino.h>
#include "sensors.h"
#include "config.h"

static volatile unsigned int pulseCount = 0;
static float lastFlowRate = 0.0;
static bool relayState = false;

// ISR (if needed for more accurate flow count)
void IRAM_ATTR flowISR() {
pulseCount++;
}

void setupSensors() {
pinMode(FLOW_SENSOR_PIN, INPUT_PULLUP);
pinMode(RELAY_PIN, OUTPUT);
digitalWrite(RELAY_PIN, LOW); // Default OFF
}

void updateFlowSensor() {
static unsigned long lastTime = 0;
if (digitalRead(FLOW_SENSOR_PIN) == LOW) {
pulseCount++;
}

if (millis() - lastTime >= 1000) {
lastFlowRate = pulseCount * 0.5; // Each pulse = 0.5 L/min
pulseCount = 0;
lastTime = millis();
}
}

float readTDS() {
int analogValue = analogRead(TDS_SENSOR_PIN);
float voltage = analogValue * (3.3 / 4095.0); // 12-bit ADC
float tds = (133.42 * voltage * voltage * voltage
- 255.86 * voltage * voltage
+ 857.39 * voltage) * 0.5;
return tds;
}

void setRelayState(bool state) {
relayState = state;
digitalWrite(RELAY_PIN, relayState ? HIGH : LOW);
}

bool getRelayState() {
return relayState;
}

void publishSensorData(PubSubClient &client) {
float tds = readTDS();
updateFlowSensor();

String payload = "{";
payload += "\"tds\":" + String(tds, 2) + ",";
payload += "\"flow\":" + String(lastFlowRate, 2) + ",";
payload += "\"relay\":\"" + String(relayState ? "ON" : "OFF") + "\"";
payload += "}";

client.publish(MQTT_TOPIC_STATUS, payload.c_str());
Serial.print("📤 Published: ");
Serial.println(payload);
}