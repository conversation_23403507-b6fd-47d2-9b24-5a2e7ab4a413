#ifndef CONFIG_H
#define CONFIG_H

// Device identity — change this per device
#define DEVICE_ID "device_001" // ← Give each device a unique ID

// GPIO Assignments
#define RELAY_PIN 27
#define FLOW_SENSOR_PIN 14
#define TDS_SENSOR_PIN 34

// MQTT Broker Settings
#define MQTT_SERVER "***************" // Replace with EMQX IP or domain
#define MQTT_PORT 1883
#define MQTT_CLIENT_ID DEVICE_ID

// MQTT Topics — dynamic per device
#define MQTT_TOPIC_STATUS "device/" DEVICE_ID "/status"
#define MQTT_TOPIC_CONTROL "device/" DEVICE_ID "/control"

#endif