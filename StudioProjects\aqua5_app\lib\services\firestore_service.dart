import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/subscription_plan.dart';

class FirestoreService {
  static final FirestoreService _instance = FirestoreService._internal();
  factory FirestoreService() => _instance;
  FirestoreService._internal();

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static const String _plansCollection = 'subscription_plans';

  // Add a new subscription plan
  Future<String> addPlan(SubscriptionPlan plan) async {
    try {
      final planData = plan.toMap();
      planData['createdAt'] = FieldValue.serverTimestamp();
      planData['updatedAt'] = FieldValue.serverTimestamp();
      
      final docRef = await _firestore.collection(_plansCollection).add(planData);
      return docRef.id;
    } catch (e) {
      throw Exception('Failed to add plan: $e');
    }
  }

  // Get all subscription plans
  Future<List<SubscriptionPlan>> getPlans() async {
    try {
      final querySnapshot = await _firestore
          .collection(_plansCollection)
          .where('isActive', isEqualTo: true)
          .orderBy('price')
          .get();

      return querySnapshot.docs
          .map((doc) => SubscriptionPlan.fromMap(doc.data(), doc.id))
          .toList();
    } catch (e) {
      throw Exception('Failed to get plans: $e');
    }
  }

  // Get plans as a stream for real-time updates
  Stream<List<SubscriptionPlan>> getPlansStream() {
    return _firestore
        .collection(_plansCollection)
        .where('isActive', isEqualTo: true)
        .orderBy('price')
        .snapshots()
        .map((snapshot) => snapshot.docs
            .map((doc) => SubscriptionPlan.fromMap(doc.data(), doc.id))
            .toList());
  }

  // Update an existing subscription plan
  Future<void> updatePlan(String planId, SubscriptionPlan plan) async {
    try {
      final planData = plan.toMap();
      planData['updatedAt'] = FieldValue.serverTimestamp();
      
      await _firestore.collection(_plansCollection).doc(planId).update(planData);
    } catch (e) {
      throw Exception('Failed to update plan: $e');
    }
  }

  // Delete a subscription plan (soft delete by setting isActive to false)
  Future<void> deletePlan(String planId) async {
    try {
      await _firestore.collection(_plansCollection).doc(planId).update({
        'isActive': false,
        'updatedAt': FieldValue.serverTimestamp(),
      });
    } catch (e) {
      throw Exception('Failed to delete plan: $e');
    }
  }

  // Get a specific plan by ID
  Future<SubscriptionPlan?> getPlanById(String planId) async {
    try {
      final doc = await _firestore.collection(_plansCollection).doc(planId).get();
      
      if (doc.exists && doc.data() != null) {
        return SubscriptionPlan.fromMap(doc.data()!, doc.id);
      }
      return null;
    } catch (e) {
      throw Exception('Failed to get plan: $e');
    }
  }

  // Check if a plan name already exists
  Future<bool> planNameExists(String planName, {String? excludePlanId}) async {
    try {
      Query query = _firestore
          .collection(_plansCollection)
          .where('planName', isEqualTo: planName)
          .where('isActive', isEqualTo: true);

      final querySnapshot = await query.get();
      
      if (excludePlanId != null) {
        return querySnapshot.docs.any((doc) => doc.id != excludePlanId);
      }
      
      return querySnapshot.docs.isNotEmpty;
    } catch (e) {
      throw Exception('Failed to check plan name: $e');
    }
  }

  // Get plans count for admin dashboard
  Future<int> getPlansCount() async {
    try {
      final querySnapshot = await _firestore
          .collection(_plansCollection)
          .where('isActive', isEqualTo: true)
          .get();
      
      return querySnapshot.docs.length;
    } catch (e) {
      throw Exception('Failed to get plans count: $e');
    }
  }

  // Create default plans if none exist
  Future<void> createDefaultPlans() async {
    try {
      final existingPlans = await getPlans();
      if (existingPlans.isNotEmpty) return;

      final defaultPlans = [
        SubscriptionPlan(
          id: '',
          planName: 'Basic',
          price: 4.99,
          dailyUsage: 10.0,
          planValidityMonths: 1,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
          features: ['10L daily water quota', 'Basic TDS monitoring', 'Email support'],
        ),
        SubscriptionPlan(
          id: '',
          planName: 'Premium',
          price: 9.99,
          dailyUsage: 50.0,
          planValidityMonths: 1,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
          features: ['50L daily water quota', 'Advanced TDS monitoring', 'Priority support', 'Usage history'],
        ),
        SubscriptionPlan(
          id: '',
          planName: 'Enterprise',
          price: 29.99,
          dailyUsage: double.infinity,
          planValidityMonths: 1,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
          features: ['Unlimited daily water quota', 'Real-time monitoring', '24/7 support', 'Advanced analytics', 'Multiple devices'],
        ),
      ];

      for (final plan in defaultPlans) {
        await addPlan(plan);
      }
    } catch (e) {
      throw Exception('Failed to create default plans: $e');
    }
  }
}
