import 'package:aqua5_app/colors.dart';
import 'package:flutter/material.dart';

class CustomSwitch extends StatelessWidget {
  final bool isOn;

  final ValueChanged<bool> onChanged;

  final bool isLoading;

  const CustomSwitch({
    super.key,
    required this.isOn,
    required this.isLoading,
    required this.onChanged,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {},
      child: Container(
        width: 200,
        height: 200,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(40),
          shape: BoxShape.rectangle,
          color: AppColors.accent.withOpacity(isOn ? 1.0 : 0.25),
          boxShadow: isOn
              ? [
                  BoxShadow(
                    blurStyle: BlurStyle.normal,
                    color: AppColors.accent,
                    blurRadius: 20,
                  ),
                ]
              : [],
        ),
        child: Center(
          child: isLoading
              ? CircularProgressIndicator(
                  color: Colors.white,
                )
              : Text(
                  !isOn ? 'ON' : 'OFF',
                  style: TextStyle(
                    fontSize: 50,
                    color: !isOn ? Colors.white : Colors.white,
                    fontWeight: FontWeight.w600,
                    fontFamily: 'Cairo',
                  ),
                ),
        ),
      ),
    );
  }
}
