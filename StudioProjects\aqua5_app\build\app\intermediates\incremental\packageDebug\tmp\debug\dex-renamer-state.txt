#Mon May 26 21:00:32 EEST 2025
path.4=1/classes.dex
path.3=10/classes.dex
path.2=0/classes.dex
path.1=0/classes.dex
path.5=classes2.dex
path.0=classes.dex
base.4=C\:\\Users\\CITY_LAP\\StudioProjects\\aqua5_app\\build\\app\\intermediates\\dex\\debug\\mergeProjectDexDebug\\1\\classes.dex
base.3=C\:\\Users\\CITY_LAP\\StudioProjects\\aqua5_app\\build\\app\\intermediates\\dex\\debug\\mergeProjectDexDebug\\10\\classes.dex
base.2=C\:\\Users\\CITY_LAP\\StudioProjects\\aqua5_app\\build\\app\\intermediates\\dex\\debug\\mergeProjectDexDebug\\0\\classes.dex
base.1=C\:\\Users\\CITY_LAP\\StudioProjects\\aqua5_app\\build\\app\\intermediates\\dex\\debug\\mergeLibDexDebug\\0\\classes.dex
base.0=C\:\\Users\\CITY_LAP\\StudioProjects\\aqua5_app\\build\\app\\intermediates\\dex\\debug\\mergeExtDexDebug\\classes.dex
renamed.3=classes4.dex
renamed.2=classes3.dex
renamed.1=classes2.dex
renamed.0=classes.dex
renamed.5=classes6.dex
renamed.4=classes5.dex
base.5=C\:\\Users\\CITY_LAP\\StudioProjects\\aqua5_app\\build\\app\\intermediates\\dex\\debug\\mergeExtDexDebug\\classes2.dex
