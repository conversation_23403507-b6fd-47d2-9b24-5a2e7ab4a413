#include <WiFiManager.h>
#include <PubSubClient.h>
#include "config.h"
#include "sensors.h"

WiFiClient espClient;
PubSubClient mqtt(espClient);

void mqttCallback(char* topic, byte* payload, unsigned int length) {
String msg;
for (unsigned int i = 0; i < length; i++) {
msg += (char)payload[i];
}

Serial.print("📩 MQTT Control: ");
Serial.println(msg);

if (String(topic) == MQTT_TOPIC_CONTROL) {
if (msg == "RELAY_ON") {
setRelayState(true);
} else if (msg == "RELAY_OFF") {
setRelayState(false);
}
}
}

void reconnectMQTT() {
while (!mqtt.connected()) {
Serial.print("Connecting to MQTT... ");
if (mqtt.connect(MQTT_CLIENT_ID)) {
Serial.println("connected.");
mqtt.subscribe(MQTT_TOPIC_CONTROL);
} else {
Serial.print("failed, rc=");
Serial.print(mqtt.state());
delay(3000);
}
}
}

void setup() {
Serial.begin(115200);
WiFi.mode(WIFI_STA);

WiFiManager wm;
if (!wm.autoConnect("AQUA5 SETUP")) {
ESP.restart();
}

setupSensors();

mqtt.setServer(MQTT_SERVER, MQTT_PORT);
mqtt.setCallback(mqttCallback);

reconnectMQTT();
}

void loop() {
if (!mqtt.connected()) {
reconnectMQTT();
}
mqtt.loop();

publishSensorData(mqtt);
delay(10000);
}
