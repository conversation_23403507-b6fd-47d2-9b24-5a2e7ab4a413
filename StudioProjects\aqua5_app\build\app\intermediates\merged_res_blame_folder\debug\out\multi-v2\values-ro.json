{"logs": [{"outputFile": "com.example.aqua5_app-mergeDebugResources-34:/values-ro/values-ro.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b2884dbf4ee3398b2907745768baeed2\\transformed\\core-1.13.1\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,355,454,556,665,782", "endColumns": "97,101,99,98,101,108,116,100", "endOffsets": "148,250,350,449,551,660,777,878"}, "to": {"startLines": "4,5,6,7,8,9,10,33", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "286,384,486,586,685,787,896,3701", "endColumns": "97,101,99,98,101,108,116,100", "endOffsets": "379,481,581,680,782,891,1008,3797"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c84e8a146159499b9c85f9e0df490157\\transformed\\jetified-play-services-base-18.1.0\\res\\values-ro\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,296,452,577,682,829,957,1076,1181,1339,1445,1600,1728,1870,2032,2099,2162", "endColumns": "102,155,124,104,146,127,118,104,157,105,154,127,141,161,66,62,77", "endOffsets": "295,451,576,681,828,956,1075,1180,1338,1444,1599,1727,1869,2031,2098,2161,2239"}, "to": {"startLines": "11,12,13,14,15,16,17,18,20,21,22,23,24,25,26,27,28", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1013,1120,1280,1409,1518,1669,1801,1924,2177,2339,2449,2608,2740,2886,3052,3123,3190", "endColumns": "106,159,128,108,150,131,122,108,161,109,158,131,145,165,70,66,81", "endOffsets": "1115,1275,1404,1513,1664,1796,1919,2028,2334,2444,2603,2735,2881,3047,3118,3185,3267"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ab921d2504dc4ffdb3a2cd4464883cfe\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-ro\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "139", "endOffsets": "334"}, "to": {"startLines": "19", "startColumns": "4", "startOffsets": "2033", "endColumns": "143", "endOffsets": "2172"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ff2b779d4ecf08070f3e2c7f20fd7758\\transformed\\jetified-credentials-1.2.0-rc01\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,165", "endColumns": "109,120", "endOffsets": "160,281"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bb5a5125e57e439427cd66fb993867a0\\transformed\\browser-1.4.0\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,162,264,379", "endColumns": "106,101,114,104", "endOffsets": "157,259,374,479"}, "to": {"startLines": "29,30,31,32", "startColumns": "4,4,4,4", "startOffsets": "3272,3379,3481,3596", "endColumns": "106,101,114,104", "endOffsets": "3374,3476,3591,3696"}}]}]}