^C:\USERS\<USER>\STUDIOPROJECTS\AQUA5_APP\BUILD\WINDOWS\X64\CMAKEFILES\6FD28D223EA2F0691F630483E5AC9912\FLUTTER_WINDOWS.DLL.RULE
setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E env FLUTTER_ROOT=C:\src\flutter PROJECT_DIR=C:\Users\<USER>\StudioProjects\aqua5_app FLUTTER_ROOT=C:\src\flutter FLUTTER_EPHEMERAL_DIR=C:\Users\<USER>\StudioProjects\aqua5_app\windows\flutter\ephemeral PROJECT_DIR=C:\Users\<USER>\StudioProjects\aqua5_app FLUTTER_TARGET=C:\Users\<USER>\StudioProjects\aqua5_app\lib\main.dart DART_OBFUSCATION=false TRACK_WIDGET_CREATION=true TREE_SHAKE_ICONS=false PACKAGE_CONFIG=C:\Users\<USER>\StudioProjects\aqua5_app\.dart_tool\package_config.json C:/src/flutter/packages/flutter_tools/bin/tool_backend.bat windows-x64 Debug
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^C:\USERS\<USER>\STUDIOPROJECTS\AQUA5_APP\BUILD\WINDOWS\X64\CMAKEFILES\642F874087ABBBFF22EB154596DA7991\FLUTTER_ASSEMBLE.RULE
setlocal
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^C:\USERS\<USER>\STUDIOPROJECTS\AQUA5_APP\WINDOWS\FLUTTER\CMAKELISTS.TXT
setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SC:/Users/<USER>/StudioProjects/aqua5_app/windows -BC:/Users/<USER>/StudioProjects/aqua5_app/build/windows/x64 --check-stamp-file C:/Users/<USER>/StudioProjects/aqua5_app/build/windows/x64/flutter/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
