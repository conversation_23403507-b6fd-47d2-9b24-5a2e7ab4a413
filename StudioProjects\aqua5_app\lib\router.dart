import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'app_state.dart';
import 'screens/home_screen.dart';
import 'screens/subscription_screen.dart';
import 'screens/notifications_screen.dart';
import 'screens/admin_screen.dart';
import 'screens/settings_screen.dart';
import 'screens/profile_screen.dart';
import 'screens/device_binding_screen.dart';
import 'screens/login_screen.dart';
import 'screens/signup_screen.dart';
import 'screens/base_page.dart';

import 'screens/password_reset_screen.dart';
import 'auth_wrapper.dart';

class AppRouter {
  static Route<dynamic> generateRoute(RouteSettings settings) {
    switch (settings.name) {
      case '/':
        return MaterialPageRoute(
          builder: (_) => const SettingsScreen(),
        );
      case '/home':
        return MaterialPageRoute(
          builder: (_) => const BasePage(
            currentIndex: 0,
            showFloatingActionButton: true,
            child: HomeScreen(),
          ),
        );
      case '/signup':
        return MaterialPageRoute(
          builder: (_) => const SignupScreen(),
        );
      case '/login':
        return MaterialPageRoute(
          builder: (_) => const LoginScreen(),
        );
      case '/password-reset':
        return MaterialPageRoute(
          builder: (_) => const PasswordResetScreen(),
        );
      case '/subscription':
        return MaterialPageRoute(
          builder: (_) => const BasePage(
            currentIndex: 2,
            showAppBar: true,
            title: 'Subscription',
            child: SubscriptionsScreen(),
          ),
        );
      case '/notifications':
        return MaterialPageRoute(
          builder: (_) => const BasePage(
            currentIndex: 1,
            showAppBar: true,
            title: 'Notifications',
            child: NotificationsScreen(),
          ),
        );
      case '/admin':
        return MaterialPageRoute(
          builder: (context) {
            final appState = Provider.of<AppState>(context, listen: false);
            return appState.isAdmin
                ? const AdminScreen()
                : const BasePage(
                    currentIndex: 0,
                    showFloatingActionButton: true,
                    child: HomeScreen(),
                  );
          },
        );
      case '/settings':
        return MaterialPageRoute(
          builder: (_) => const BasePage(
            currentIndex: 3,
            showAppBar: true,
            title: 'Settings',
            child: SettingsScreen(),
          ),
        );
      case '/profile':
        return MaterialPageRoute(
          builder: (_) => const BasePage(
            currentIndex: 3,
            showAppBar: true,
            title: 'Profile',
            child: ProfileScreen(),
          ),
        );
      case '/device-binding':
        return MaterialPageRoute(
          builder: (_) => const BasePage(
            currentIndex: 0,
            showAppBar: true,
            title: 'Add Device',
            child: DeviceBindingScreen(),
          ),
        );
      default:
        return MaterialPageRoute(
          builder: (_) => Scaffold(
            body: Center(
              child: Text('No route defined for ${settings.name}'),
            ),
          ),
        );
    }
  }
}
