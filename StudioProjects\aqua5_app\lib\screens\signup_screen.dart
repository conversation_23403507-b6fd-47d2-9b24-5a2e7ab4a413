import 'package:aqua5_app/app_state.dart';
import 'package:aqua5_app/colors.dart';
import 'package:aqua5_app/screens/otp_verification_screen.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../services/auth_service.dart';
import '../utils/validators.dart';

class SignupScreen extends StatefulWidget {
  const SignupScreen({super.key});

  @override
  State<SignupScreen> createState() => _SignupScreenState();
}

class _SignupScreenState extends State<SignupScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _emailOrPhoneController = TextEditingController();
  final _passwordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();
  final _authService = AuthService();

  bool _isEmailSignup = true;
  bool _isLoading = false;
  String? _errorMessage;
  bool _acceptTerms = false;

  @override
  void dispose() {
    _nameController.dispose();
    _emailOrPhoneController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    super.dispose();
  }

  // Handle email signup
  Future<void> _handleEmailSignup() async {
    if (!_formKey.currentState!.validate() || !_acceptTerms) {
      if (!_acceptTerms) {
        setState(() {
          _errorMessage = 'You must accept the terms and conditions';
        });
      }
      return;
    }

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    final result = await _authService.signUpWithEmail(
      _emailOrPhoneController.text.trim(),
      _passwordController.text,
    );

    setState(() {
      _isLoading = false;
    });

    if (result.success) {
      // Update user profile with name
      await _authService.updateProfile(
        displayName: _nameController.text.trim(),
      );

      // Navigate to home (will be handled by AuthWrapper)
      if (mounted) {
        Navigator.of(context).pop();
      }
    } else {
      setState(() {
        _errorMessage = result.errorMessage;
      });
    }
  }

  // Handle phone signup (send OTP)
  Future<void> _handlePhoneSignup() async {
    if (!_formKey.currentState!.validate() || !_acceptTerms) {
      if (!_acceptTerms) {
        setState(() {
          _errorMessage = 'You must accept the terms and conditions';
        });
      }
      return;
    }

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    final result = await _authService.sendOTP(
      _emailOrPhoneController.text.trim(),
    );

    setState(() {
      _isLoading = false;
    });

    if (result.success) {
      // Navigate to OTP verification screen
      if (mounted) {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => OtpVerificationScreen(
              phoneNumber: _emailOrPhoneController.text.trim(),
              verificationId: result.errorMessage ??
                  '', // Using errorMessage to pass verificationId
            ),
          ),
        );
      }
    } else {
      setState(() {
        _errorMessage = result.errorMessage;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
        child: Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.black),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Form(
          key: _formKey,
          child: Consumer<AppState>(
            builder: (context, appState, child) {
              return SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const SizedBox(height: 20),
                    Text(
                      'Create Account',
                      style: TextStyle(
                        fontWeight: FontWeight.w800,
                        fontSize: 28,
                        color: const Color.fromARGB(255, 0, 0, 0),
                        letterSpacing: 0.5,
                      ),
                    ),
                    Text(
                      'Join Aqua5 today',
                      style: TextStyle(
                        fontWeight: FontWeight.w500,
                        fontSize: 14,
                        color: const Color.fromARGB(255, 49, 49, 49)
                            .withAlpha(178),
                      ),
                    ),
                    const SizedBox(height: 32),
                    Container(
                      width: double.infinity,
                      decoration: BoxDecoration(
                        color: AppColors.cardBackground,
                        // AppColors.cardBackground.withOpacity(0.9),

                        borderRadius: BorderRadius.circular(16),
                        // boxShadow: [
                        //   BoxShadow(
                        //     color: AppColors.accent.withOpacity(0.2),
                        //     blurRadius: 12,
                        //     offset: const Offset(0, 6),
                        //   ),
                        // ],
                      ),
                      child: Padding(
                        padding: const EdgeInsets.all(20.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                GestureDetector(
                                  onTap: () {
                                    setState(() {
                                      _isEmailSignup = true;
                                    });
                                  },
                                  child: Text(
                                    'Email',
                                    style: TextStyle(
                                      fontSize: 16,
                                      color: _isEmailSignup
                                          ? Colors.blue
                                          : Colors.blue.withAlpha(128),
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                ),
                                GestureDetector(
                                  onTap: () {
                                    setState(() {
                                      _isEmailSignup = false;
                                    });
                                  },
                                  child: Text(
                                    'Phone',
                                    style: TextStyle(
                                      fontSize: 16,
                                      color: !_isEmailSignup
                                          ? Colors.blue
                                          : Colors.blue.withAlpha(128),
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 20),
                            TextFormField(
                              controller: _nameController,
                              decoration: InputDecoration(
                                labelText: 'Name',
                                labelStyle: TextStyle(
                                  color: const Color.fromARGB(255, 11, 83, 143)
                                      .withAlpha(178),
                                ),
                                filled: true,
                                fillColor: AppColors.accent.withAlpha(25),
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(12),
                                  borderSide: BorderSide.none,
                                ),
                                errorStyle: TextStyle(color: Colors.red),
                              ),
                              style: TextStyle(
                                color: Colors.black,
                                fontSize: 14,
                              ),
                              validator: Validators.validateName,
                            ),
                            const SizedBox(height: 16),
                            TextFormField(
                              controller: _emailOrPhoneController,
                              decoration: InputDecoration(
                                labelText:
                                    _isEmailSignup ? 'Email' : 'Phone Number',
                                labelStyle: TextStyle(
                                  color: const Color.fromARGB(255, 11, 83, 143)
                                      .withAlpha(178),
                                ),
                                filled: true,
                                fillColor: AppColors.accent.withAlpha(25),
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(12),
                                  borderSide: BorderSide.none,
                                ),
                                errorStyle: TextStyle(color: Colors.red),
                              ),
                              style: TextStyle(
                                color: Colors.black,
                                fontSize: 14,
                              ),
                              keyboardType: _isEmailSignup
                                  ? TextInputType.emailAddress
                                  : TextInputType.phone,
                              validator: _isEmailSignup
                                  ? Validators.validateEmail
                                  : Validators.validatePhone,
                            ),
                            if (_isEmailSignup) ...[
                              const SizedBox(height: 16),
                              TextFormField(
                                controller: _passwordController,
                                decoration: InputDecoration(
                                  labelText: 'Password',
                                  labelStyle: TextStyle(
                                    color:
                                        const Color.fromARGB(255, 11, 83, 143)
                                            .withAlpha(178),
                                  ),
                                  filled: true,
                                  fillColor: AppColors.accent.withAlpha(25),
                                  border: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(12),
                                    borderSide: BorderSide.none,
                                  ),
                                  errorStyle: TextStyle(color: Colors.red),
                                ),
                                style: TextStyle(
                                  color: Colors.black,
                                  fontSize: 14,
                                ),
                                obscureText: true,
                                validator: Validators.validatePassword,
                              ),
                              const SizedBox(height: 16),
                              TextFormField(
                                controller: _confirmPasswordController,
                                decoration: InputDecoration(
                                  labelText: 'Confirm Password',
                                  labelStyle: TextStyle(
                                    color:
                                        const Color.fromARGB(255, 11, 83, 143)
                                            .withAlpha(178),
                                  ),
                                  filled: true,
                                  fillColor: AppColors.accent.withAlpha(25),
                                  border: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(12),
                                    borderSide: BorderSide.none,
                                  ),
                                  errorStyle: TextStyle(color: Colors.red),
                                ),
                                style: TextStyle(
                                  color: Colors.black,
                                  fontSize: 14,
                                ),
                                obscureText: true,
                                validator: (value) =>
                                    Validators.validatePasswordMatch(
                                  _passwordController.text,
                                  value,
                                ),
                              ),
                            ],
                            if (_errorMessage != null)
                              Padding(
                                padding: const EdgeInsets.only(top: 8.0),
                                child: Text(
                                  _errorMessage!,
                                  style: TextStyle(
                                    color: Colors.red,
                                    fontSize: 14,
                                  ),
                                ),
                              ),
                            const SizedBox(height: 16),
                            Row(
                              children: [
                                Checkbox(
                                  value: _acceptTerms,
                                  onChanged: (value) {
                                    setState(() {
                                      _acceptTerms = value ?? false;
                                    });
                                  },
                                  activeColor: AppColors.accent,
                                ),
                                Expanded(
                                  child: Text(
                                    'I agree to the Terms of Service and Privacy Policy',
                                    style: TextStyle(
                                      fontSize: 12,
                                      color: Colors.black87,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 24),
                            ElevatedButton(
                              style: ElevatedButton.styleFrom(
                                minimumSize: const Size.fromHeight(52),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                backgroundColor: AppColors.accent,
                                foregroundColor: Colors.white,
                                elevation: 2,
                              ),
                              onPressed: _isLoading
                                  ? null
                                  : _isEmailSignup
                                      ? _handleEmailSignup
                                      : _handlePhoneSignup,
                              child: _isLoading
                                  ? const CircularProgressIndicator(
                                      color: Colors.white)
                                  : Text(
                                      _isEmailSignup ? 'Sign Up' : 'Send OTP',
                                      style: TextStyle(
                                        fontSize: 16,
                                        fontWeight: FontWeight.w600,
                                      ),
                                    ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    const SizedBox(height: 24),
                    Center(
                      child: TextButton(
                        onPressed: () {
                          Navigator.pop(context);
                        },
                        child: Text.rich(
                          TextSpan(
                            children: [
                              TextSpan(
                                text: 'Already have an account? ',
                                style: TextStyle(
                                  color: const Color.fromARGB(255, 0, 0, 0)
                                      .withAlpha(178),
                                  fontSize: 14,
                                ),
                              ),
                              TextSpan(
                                text: 'Log In',
                                style: TextStyle(
                                  color: AppColors.accent,
                                  fontSize: 14,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              );
            },
          ),
        ),
      ),
    ));
  }
}
